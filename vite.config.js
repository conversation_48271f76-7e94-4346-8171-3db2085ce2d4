import { defineConfig } from 'vite'
import path from 'path'
import pkgJson from './package.json'
const compName = pkgJson.name

// https://vitejs.dev/config/
export default defineConfig({
    alias: {
        [compName]: path.resolve(__dirname, 'src')
    },
    build: {
        lib: {
            entry: path.resolve(__dirname, 'src/index.js'),
            name: compName.replace(/(\w)/, function (a, b) { return b.toUpperCase() }).replace(/-(\w)/g, function (a, b) {
                return b.toUpperCase()
            })
        },
        outDir: 'build',
        rollupOptions: {
            // 请确保外部化那些你的库中不需要的依赖
            external: [],
            output: {
                // 在 UMD 构建模式下为这些外部化的依赖提供一个全局变量
                globals: {
                }
            }
        }
    }
})
