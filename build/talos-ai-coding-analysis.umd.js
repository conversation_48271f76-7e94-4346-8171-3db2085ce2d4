!function(o){"function"==typeof define&&define.amd?define(o):o()}((function(){"use strict";const o=require("axios"),{execSync:e}=require("child_process"),n=require("fs"),t=require("path"),s=require("os");function l(o,n,s,l=process.cwd()){const c=[],i=o.split("\n");console.log(`📋 检测到 ${i.length} 个文件变更`);for(let a=0;a<i.length;a++){const o=i[a];if(!o)continue;console.log(`\n📄 处理第 ${a+1}/${i.length} 个文件...`);const[d,h]=o.split("\t");if(console.log(`  - 文件: ${h}`),console.log(`  - 状态: ${"A"===d?"新增":"M"===d?"修改":d}`),!$(h)){console.log(`  ⏭️  跳过非代码文件: ${h}`);continue}let m="",u="";if("A"===d){console.log("  📥 获取新增文件内容...");try{let o;if("working-directory"===n)o=`cat "${h}"`,console.log(`    执行命令: ${o}`),m=e(o,{cwd:l}).toString();else if("staged"===n)o=`git show :${h}`,console.log(`    执行命令: ${o}`),m=e(o,{cwd:l}).toString();else{o=`git show "${n}:${h}"`,console.log(`    执行命令: ${o}`);try{m=e(o,{cwd:l}).toString()}catch(r){console.error(`    ❌ Git show命令失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o=`git show '${n}:${h}'`;console.log(`    备用命令: ${o}`),m=e(o,{cwd:l}).toString()}}u="",console.log(`    ✅ 成功获取文件内容，长度: ${m.length} 字符`)}catch(g){console.error(`    ❌ 获取新增文件 ${h} 内容失败:`,g.message);continue}}else if("M"===d){console.log("  📥 获取修改文件的原始和当前内容...");try{let o,t;o="HEAD"===s?`git show "HEAD:${h}"`:`git show "${s}:${h}"`,console.log(`    获取原始内容: ${o}`);try{u=e(o,{cwd:l}).toString()}catch(r){console.error(`    ❌ 获取原始内容失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o="HEAD"===s?`git show 'HEAD:${h}'`:`git show '${s}:${h}'`;console.log(`    备用命令: ${o}`),u=e(o,{cwd:l}).toString()}console.log(`    ✅ 原始内容长度: ${u.length} 字符`),t="working-directory"===n?`cat "${h}"`:"staged"===n?`git show :${h}`:`git show "${n}:${h}"`,console.log(`    获取当前内容: ${t}`);try{m=e(t,{cwd:l}).toString()}catch(r){console.error(`    ❌ 获取当前内容失败: ${r.message}`),console.log("    🔄 尝试备用命令格式...");const o="working-directory"===n?`cat "${h}"`:"staged"===n?`git show :${h}`:`git show '${n}:${h}'`;console.log(`    备用命令: ${o}`),m=e(o,{cwd:l}).toString()}console.log(`    ✅ 当前内容长度: ${m.length} 字符`)}catch(g){console.error(`    ❌ 获取修改文件 ${h} 内容失败:`,g.message);continue}}if(m.trim()){const o=t.extname(h).substring(1);console.log(`  📊 文件类型: ${o}`);const e=m.split("\n").length;console.log(`  📏 总行数: ${e}`),console.log("  🔢 计算变更行数...");const n=f(u,m);console.log(`  📈 变更行数: ${n}`);const s={fileName:h,fileType:o,status:d,originalContent:u,currentContent:m,totalLines:e,changedLines:n};c.push(s),console.log(`  ✅ 文件 ${h} 处理完成`)}else console.log(`  ⚠️  文件 ${h} 内容为空，跳过`)}return console.log(`\n📊 Git变更文件处理完成，共处理 ${c.length} 个有效文件`),c}function c(o){if(!o)return console.log("    ⚠️  生成代码为空"),[];console.log(`    📝 开始解析生成代码，长度: ${o.length} 字符`);const e=[],n=/--- FILE: ([^\s]+) ---\n([\s\S]*?)(?=--- FILE:|$)/g;let t,s=0;for(;null!==(t=n.exec(o));){s++;const o=t[1],n=t[2].trim();console.log(`      📄 解析文件 ${s}: ${o}`),console.log(`      📏 文件内容长度: ${n.length} 字符`),console.log(`      📊 文件行数: ${n.split("\n").length} 行`),e.push({fileName:o,content:n})}return console.log(`    ✅ 解析完成，共找到 ${e.length} 个文件`),e}function i(o,e){const n=[];for(const t of e)for(const e of t.files)if(r(o,e.fileName)){const o=e.content.split("\n");for(let l=0;l<o.length;l++){const s=o[l].trim();!s||s.startsWith("//")||s.startsWith("/*")||s.startsWith("*")||n.push({line:s,recordId:t.id,lineIndex:l,fileName:e.fileName})}const s=m(e.content);for(const l of s)n.push({isBlock:!0,content:l.content,recordId:t.id,startLine:l.startLine,endLine:l.endLine,fileName:e.fileName})}return n}function r(o,e){if(o===e)return!0;if(o.endsWith("/"+e)||e.endsWith("/"+o))return!0;return o.split("/").pop()===e.split("/").pop()}function g(o,e){const n=d(o);if(!n)return null;for(const l of e)if(!l.isBlock&&d(l.line)===n)return{recordId:l.recordId,similarity:1};let t=null,s=.8;for(const l of e)if(!l.isBlock){const o=h(n,d(l.line));o>s&&(s=o,t={recordId:l.recordId,similarity:o})}return t}function a(o,e){const n=[],t=[];let s=[];for(let l=0;l<o.length;l++){const e=o[l];0===l||e.index===o[l-1].index+1?s.push(e):(s.length>0&&t.push([...s]),s=[e])}s.length>0&&t.push(s);for(const l of t){if(l.length<3)continue;const o=l.map((o=>o.content)).join("\n");for(const t of e)if(t.isBlock){const e=h(o,t.content);if(e>=.7){for(const o of l)n.push({type:"块级匹配",lineIndex:o.index,aiRecordId:t.recordId,similarity:e,blockSize:l.length});break}}}return n}function f(o,l){if(!o)return l.split("\n").length;try{const c=t.join(s.tmpdir(),"orig_"+Date.now()),i=t.join(s.tmpdir(),"curr_"+Date.now());n.writeFileSync(c,o),n.writeFileSync(i,l);const r=e(`diff -U0 "${c}" "${i}" | grep '^+[^+]' | wc -l`).toString();return n.unlinkSync(c),n.unlinkSync(i),parseInt(r.trim(),10)}catch(c){return console.error("计算变更行数失败:",c),0}}function $(o){const e=t.extname(o).toLowerCase();return[".js",".jsx",".ts",".tsx",".vue",".html",".css",".scss",".less",".java",".py",".php",".go",".rb",".c",".cpp",".h",".cs",".swift"].includes(e)}function d(o){return o?o.trim().replace(/\s+/g," ").replace(/;\s*$/,"").toLowerCase():""}function h(o,e){if(!o||!e)return 0;if(o===e)return 1;const n=o.length,t=e.length,s=[];for(let i=0;i<=n;i++)s[i]=[i];for(let i=0;i<=t;i++)s[0][i]=i;for(let i=1;i<=n;i++)for(let n=1;n<=t;n++)o[i-1]===e[n-1]?s[i][n]=s[i-1][n-1]:s[i][n]=Math.min(s[i-1][n]+1,s[i][n-1]+1,s[i-1][n-1]+1);const l=s[n][t],c=Math.max(n,t);return 0===c?1:(c-l)/c}function m(o){if(!o)return[];const e=o.split("\n"),n=[];let t=[],s=0;for(let l=0;l<e.length;l++){const o=e[l].trim();!o||o.startsWith("//")||o.startsWith("/*")||o.startsWith("*")?(t.length>=3&&n.push({content:t.join("\n"),startLine:s,endLine:l-1}),t=[],s=l+1):t.push(e[l])}return t.length>=3&&n.push({content:t.join("\n"),startLine:s,endLine:e.length-1}),n}!async function(){const r=Date.now();console.log("🚀 开始AI代码分析...",(new Date).toISOString());try{console.log("📋 解析环境变量...");const d=!1;console.log("🔧 运行模式: "+(d?"开发模式":"生产模式"));const{pr_global_id:h,author:m,source_ref:u,target_ref:p,title:_,GIT_REPO:y,GIT_BRANCH:w}=d?{pr_global_id:"13073502",author:"yaoyan03",source_ref:"refs/heads/feature/CreatePage-YAOYAN-0523",target_ref:"release",title:"【不要合并，只是测试webhook】",GIT_REPO:"ssh://*******************/dzfe/medical-home-page.git",GIT_BRANCH:"feature/CreatePage-YAOYAN-0523"}:process.env;if(console.log("📊 PR信息:"),console.log(`  - PR ID: ${h}`),console.log(`  - 标题: ${_}`),console.log(`  - 作者: ${m}`),console.log(`  - 仓库: ${y}`),console.log(`  - 分支: ${w}`),console.log(`  - 源分支: ${u}`),console.log(`  - 目标分支: ${p}`),!function(o,e){console.log("🔍 检查分支合并条件..."),console.log(`  - 源分支: ${o}`),console.log(`  - 目标分支: ${e}`);const n=e&&(e.toLowerCase().includes("release")||e.toLowerCase().includes("master")||"main"===e.toLowerCase()),t=o?o.replace(/^refs\/heads\//,""):"",s=t&&(t.toLowerCase().startsWith("feature/")||t.toLowerCase().startsWith("bugfix/"));return console.log(`  - 清理后的源分支: ${t}`),console.log("  - 目标分支是release/master分支: "+(n?"✅":"❌")),console.log("  - 源分支是feature/bugfix分支: "+(s?"✅":"❌")),n?s?(console.log("✅ 分支合并条件验证通过，继续执行AI代码分析"),!0):(console.log("⚠️  源分支不是feature或bugfix分支，跳过AI代码分析"),!1):(console.log("⚠️  目标分支不是release或master分支，跳过AI代码分析"),!1)}(u,p))return console.log("🚫 分支合并条件不满足，流水线终止"),console.log("💡 提示：只有feature或bugfix分支合并到release或master分支时才会触发AI代码分析"),{success:!1,message:"分支合并条件不满足，只有feature或bugfix分支合并到release或master分支时才会触发AI代码分析",source_branch:u,target_branch:p};console.log("🔍 解析Git分支信息...");const I=w||e("git rev-parse --abbrev-ref HEAD").toString().trim(),A=p||"master";console.log(`✅ 当前分支: ${I}, 基准分支: ${A}`),console.log(`📍 目标仓库: ${y}`),console.log("\n📁 步骤1: 获取Git变更代码...");const S=Date.now(),D=await async function(o,c,i){const r=[];try{const u=i&&!i.includes(process.cwd())&&i.startsWith("ssh://");let p=process.cwd(),_=null;if(u){console.log(`🌐 检测到远程仓库，准备克隆: ${i}`);const o=`temp-repo-${Date.now()}`;_=t.join(s.tmpdir(),o);try{console.log(`📁 创建临时目录: ${_}`),n.mkdirSync(_,{recursive:!0}),console.log("🔄 克隆仓库到临时目录..."),e(`git clone ${i} ${_}`,{stdio:"pipe"}),console.log("✅ 仓库克隆完成"),p=_,console.log("🔄 获取所有远程分支..."),e("git fetch --all",{cwd:p,stdio:"pipe"}),console.log("✅ 远程分支获取完成")}catch(g){if(console.error("❌ 克隆仓库失败:",g.message),_&&n.existsSync(_))try{n.rmSync(_,{recursive:!0,force:!0})}catch(a){console.error("⚠️  清理临时目录失败:",a.message)}throw g}}console.log(`🔍 在目录 ${p} 中检查分支...`);let y=!1;try{e(`git rev-parse --verify ${o}`,{cwd:p,stdio:"pipe"}),console.log(`✅ 当前分支 ${o} 存在`),y=!0}catch(d){console.log(`⚠️  本地分支 ${o} 不存在，尝试其他方式...`);try{e(`git rev-parse --verify origin/${o}`,{cwd:p,stdio:"pipe"}),console.log(`✅ 远程分支 origin/${o} 存在`),o=`origin/${o}`,y=!0}catch(h){if(console.log(`⚠️  远程分支 origin/${o} 也不存在`),u)try{const o=e("git branch -a",{cwd:p}).toString();console.log(`📋 可用分支列表:\n${o}`)}catch(m){console.log("⚠️  无法列出分支:",m.message)}throw new Error(`分支 ${o} 在仓库中不存在`)}}try{e(`git rev-parse --verify ${c}`,{cwd:p,stdio:"pipe"}),console.log(`✅ 基准分支 ${c} 存在`)}catch(d){console.log(`⚠️  基准分支 ${c} 不存在，尝试远程分支...`);try{e(`git rev-parse --verify origin/${c}`,{cwd:p,stdio:"pipe"}),c=`origin/${c}`,console.log(`✅ 使用远程分支: ${c}`)}catch(h){console.log(`⚠️  远程分支 origin/${c} 也不存在，使用HEAD~1`),c="HEAD~1"}}console.log(`🔍 执行Git diff命令: git diff --name-status --diff-filter=AM ${c}..${o}`);const w=e(`git diff --name-status --diff-filter=AM ${c}..${o}`,{cwd:p}).toString().trim();if(!w){console.log("⚠️  没有检测到变更文件，尝试其他策略..."),console.log("🔄 策略1: 获取最近提交的变更...");try{const n="git diff --name-status HEAD~1 HEAD";console.log(`执行命令: ${n}`);const t=e(n,{cwd:p}).toString().trim();if(t)return console.log(`✅ 找到最近提交的变更: ${t.split("\n").length} 个文件`),l(t,o,"HEAD~1",p)}catch(d){console.log("❌ 策略1失败:",d.message)}console.log("🔄 策略2: 获取工作目录中的变更...");try{const o="git diff --name-status";console.log(`执行命令: ${o}`);const n=e(o,{cwd:p}).toString().trim();if(n)return console.log(`✅ 找到工作目录变更: ${n.split("\n").length} 个文件`),l(n,"working-directory","HEAD",p)}catch(d){console.log("❌ 策略2失败:",d.message)}console.log("🔄 策略3: 获取暂存区变更...");try{const o="git diff --name-status --cached";console.log(`执行命令: ${o}`);const n=e(o,{cwd:p}).toString().trim();if(n)return console.log(`✅ 找到暂存区变更: ${n.split("\n").length} 个文件`),l(n,"staged","HEAD",p)}catch(d){console.log("❌ 策略3失败:",d.message)}console.log("🔄 策略4: 分析最近的合并提交...");try{const o=await async function(o){console.log("  🔍 查找最近的合并提交...");try{const n='git log --merges --oneline -1 --format="%H"';console.log(`  执行命令: ${n}`);const t=e(n,{cwd:o}).toString().trim();if(!t)return console.log("  ⚠️  没有找到合并提交"),[];console.log(`  📍 找到合并提交: ${t}`);const s=`git rev-list --parents -n 1 ${t}`;console.log(`  执行命令: ${s}`);const c=e(s,{cwd:o}).toString().trim().split(" ").slice(1);if(c.length<2)return console.log("  ⚠️  合并提交的父提交数量不足"),[];console.log(`  👥 合并提交的父提交: ${c.join(", ")}`);const i=c[0],r=c[1];console.log(`  🔄 比较父提交差异: ${i}..${r}`);const g=`git diff --name-status --diff-filter=AM ${i}..${r}`;console.log(`  执行命令: ${g}`);const a=e(g,{cwd:o}).toString().trim();return a?(console.log(`  ✅ 找到合并变更: ${a.split("\n").length} 个文件`),l(a,r,i,o)):(console.log("  ⚠️  父提交之间没有差异"),[])}catch(n){throw console.log(`  ❌ 分析合并提交失败: ${n.message}`),n}}(p);if(o&&o.length>0)return console.log(`✅ 从合并提交中找到变更: ${o.length} 个文件`),o}catch(d){console.log("❌ 策略4失败:",d.message)}console.log("🔄 策略4.1: 分析远程分支的合并提交...");try{const n=await async function(o,n,t){console.log("  🔍 查找远程分支的合并提交...");try{console.log(`  📋 在分支 ${n} 上查找合并提交...`);const s=`git log ${n} --merges --oneline -10 --format="%H %s"`;console.log(`  执行命令: ${s}`);const c=e(s,{cwd:t}).toString().trim();if(!c)return console.log("  ⚠️  没有找到合并提交"),[];const i=c.split("\n");console.log(`  📍 找到 ${i.length} 个合并提交`);const r=o.replace(/^origin\//,"");for(const o of i){const[n,...s]=o.split(" "),c=s.join(" ");if(console.log(`  🔍 检查合并提交: ${n} - ${c}`),c.toLowerCase().includes(r.toLowerCase())){console.log(`  ✅ 找到匹配的合并提交: ${n}`);const o=`git diff --name-status --diff-filter=AM ${n}~1 ${n}`;console.log(`  执行命令: ${o}`);const s=e(o,{cwd:t}).toString().trim();if(s)return console.log(`  ✅ 找到合并变更: ${s.split("\n").length} 个文件`),l(s,n,`${n}~1`,t)}}return console.log("  ⚠️  没有找到匹配的合并提交"),[]}catch(s){throw console.log(`  ❌ 分析远程合并提交失败: ${s.message}`),s}}(o,c,p);if(n&&n.length>0)return console.log(`✅ 从远程合并提交中找到变更: ${n.length} 个文件`),n}catch(d){console.log("❌ 策略4.1失败:",d.message)}console.log("🔄 策略5: 使用merge-base查找变更...");try{const n=await async function(o,n,t){console.log("  🔍 使用merge-base查找共同祖先...");try{const c=[[o,n],[o.replace(/^origin\//,""),n.replace(/^origin\//,"")],[o,n.replace(/^origin\//,"")],[o.replace(/^origin\//,""),n]];for(const[o,n]of c)try{console.log(`  📋 尝试分支组合: ${o}, ${n}`);const s=`git merge-base ${n} ${o}`;console.log(`  执行命令: ${s}`);const c=e(s,{cwd:t}).toString().trim();if(!c){console.log("  ⚠️  没有找到共同祖先");continue}console.log(`  📍 找到共同祖先: ${c}`);const i=`git diff --name-status --diff-filter=AM ${c}..${o}`;console.log(`  执行命令: ${i}`);const r=e(i,{cwd:t}).toString().trim();if(!r){console.log("  ⚠️  与共同祖先没有差异");continue}return console.log(`  ✅ 找到变更: ${r.split("\n").length} 个文件`),l(r,o,c,t)}catch(s){console.log(`  ⚠️  分支组合 ${o}, ${n} 失败: ${s.message}`);continue}return console.log("  ❌ 所有分支组合都失败了"),[]}catch(c){throw console.log(`  ❌ merge-base分析失败: ${c.message}`),c}}(o,c,p);if(n&&n.length>0)return console.log(`✅ 从merge-base中找到变更: ${n.length} 个文件`),n}catch(d){console.log("❌ 策略5失败:",d.message)}console.log("🔄 策略6: 检查CI/CD环境变量...");try{const o=function(){console.log("  🔍 检查常见的CI/CD环境变量...");const o=["CI_MERGE_REQUEST_DIFF_BASE_SHA","CI_MERGE_REQUEST_DIFF_HEAD_SHA","GITLAB_CI_CHANGED_FILES","GITHUB_CHANGED_FILES","JENKINS_CHANGED_FILES","CHANGED_FILES","GIT_DIFF_FILES","MERGE_REQUEST_CHANGED_FILES"];for(const e of o){const o=process.env[e];if(o){console.log(`  ✅ 找到环境变量 ${e}: ${o}`);const n=o.split(/[,\s\n]+/).filter((o=>o.trim()));if(n.length>0)return n}}return console.log("  ⚠️  没有找到包含变更文件的环境变量"),[]}();if(o&&o.length>0)return console.log(`✅ 从CI环境变量中找到变更: ${o.length} 个文件`),await async function(o,s){console.log(`  📋 处理 ${o.length} 个变更文件...`);const l=[];for(let i=0;i<o.length;i++){const r=o[i].trim();if(r)if(console.log(`  📄 处理第 ${i+1}/${o.length} 个文件: ${r}`),$(r))try{const o=t.join(s,r);if(!n.existsSync(o)){console.log(`    ⚠️  文件不存在: ${r}`);continue}const c=n.readFileSync(o,"utf8"),i=c.split("\n").length;let g="",a=i;try{g=e(`git show HEAD:${r}`,{cwd:s}).toString(),a=f(g,c)}catch(d){console.log(`    💡 无法获取原始内容，可能是新文件: ${r}`)}const $={fileName:r,fileType:t.extname(r).substring(1),status:g?"M":"A",originalContent:g,currentContent:c,totalLines:i,changedLines:a};l.push($),console.log(`    ✅ 文件 ${r} 处理完成`)}catch(c){console.log(`    ❌ 处理文件 ${r} 失败: ${c.message}`)}else console.log(`    ⏭️  跳过非代码文件: ${r}`)}return console.log(`  📊 变更文件处理完成，共处理 ${l.length} 个有效文件`),l}(o,p)}catch(d){console.log("❌ 策略6失败:",d.message)}return console.log("⚠️  所有策略都未找到变更文件"),r}const I=l(w,o,c,p);if(_&&n.existsSync(_))try{console.log(`🧹 清理临时目录: ${_}`),n.rmSync(_,{recursive:!0,force:!0}),console.log("✅ 临时目录清理完成")}catch(a){console.error("⚠️  清理临时目录失败:",a.message)}return I}catch(u){console.error("❌ 获取Git变更失败:",u),console.error("错误详情:",u.stack)}return r}(I,A,y),L=Date.now()-S;console.log(`✅ 步骤1完成: 获取到 ${D.length} 个变更文件 (耗时: ${L}ms)`),console.log("\n🤖 步骤2: 获取AI生成的代码记录...");const E=Date.now(),C=await async function(e,n){try{const t="https://m.51ping.com/offlinecode/admin/queryCodeGenRecords";console.log(`🌐 准备请求AI代码记录API: ${t}`);const s={createdAt:"",gitUsername:"",gitRepository:e,gitBranch:n,pageNum:1,pageSize:1e3};console.log("📤 请求参数:"),console.log(`  - 仓库: ${e}`),console.log(`  - 分支: ${n}`),console.log(`  - 页码: ${s.pageNum}`),console.log(`  - 页大小: ${s.pageSize}`),console.log("🚀 发送API请求...");const l=Date.now(),i=await o.post(t,s),r=Date.now()-l;if(console.log(`✅ API请求完成 (耗时: ${r}ms)`),console.log(`📊 响应状态: ${i.status}`),console.log("📦 响应数据类型: "+typeof i.data),i.data&&Array.isArray(i.data.list)){const o=i.data.list;console.log(`📋 获取到 ${o.length} 条原始记录`);const e=[];for(let n=0;n<o.length;n++){const t=o[n];console.log(`\n🔄 处理第 ${n+1}/${o.length} 条记录...`),console.log(`  - 记录ID: ${t.id}`),console.log(`  - 用户: ${t.gitUsername}`),console.log(`  - 创建时间: ${t.createdAt}`),console.log(`  - 生成时间戳: ${t.generationTimestamp}`),console.log("  📄 解析生成的代码...");const s=c(t.generatedCode);console.log(`  📁 解析出 ${s.length} 个文件`);const l={id:t.id,timestamp:t.generationTimestamp,gitUsername:t.gitUsername,generatedAt:t.createdAt,files:s};e.push(l),console.log(`  ✅ 记录 ${t.id} 处理完成`)}return console.log(`\n📊 AI记录处理完成，共处理 ${e.length} 条有效记录`),e}return console.log("⚠️  API响应数据格式不正确或为空"),console.log("响应数据:",i.data),[]}catch(t){return console.error("❌ 获取AI生成代码记录失败:",t),t.response&&(console.error("HTTP状态码:",t.response.status),console.error("响应数据:",t.response.data)),console.error("错误详情:",t.stack),[]}}(y,w),b=Date.now()-E;console.log(`✅ 步骤2完成: 获取到 ${C.length} 条AI生成记录 (耗时: ${b}ms)`),console.log("\n🔄 步骤3: 进行代码比对分析...");const N=Date.now(),x=function(o,e){console.log(`🔄 开始代码比对分析，共 ${o.length} 个文件需要分析`);const n=[];for(let t=0;t<o.length;t++){const s=o[t];console.log(`\n📄 分析第 ${t+1}/${o.length} 个文件: ${s.fileName}`),console.log(`  📊 文件信息: ${s.fileType} 类型，${s.totalLines} 总行数，${s.changedLines} 变更行数`),console.log("  🔍 查找相关的AI生成内容...");const l=i(s.fileName,e);console.log(`  📋 找到 ${l.length} 条相关AI生成内容`);const c=s.currentContent.split("\n");console.log(`  📏 文件共 ${c.length} 行`);const r={fileName:s.fileName,fileType:s.fileType,totalLines:s.totalLines,changedLines:s.changedLines,matchedLines:0,aiCodePortion:0,aiMatchDetails:[]};if(l.length>0){console.log("  🔄 开始行级比对...");const o=new Set;let e=!1,n=!1,t=0;for(let s=0;s<c.length;s++){const n=c[s].trim();if(!n)continue;const i=g(n,l);i&&(t++,o.add(s),e=!0,r.aiMatchDetails.push({type:"行级匹配",lineIndex:s,aiRecordId:i.recordId,similarity:i.similarity,lineContent:n}))}console.log(`  ✅ 行级比对完成，匹配 ${t} 行`);const i=[];for(let s=0;s<c.length;s++)o.has(s)||i.push({index:s,content:c[s]});if(i.length>0){console.log(`  🔄 开始块级比对，${i.length} 行未匹配...`);const e=a(i,l);console.log(`  ✅ 块级比对完成，匹配 ${e.length} 行`),e.length>0&&(n=!0);for(const n of e)o.add(n.lineIndex),r.aiMatchDetails.push(n)}r.detectionMethod=e&&n?"行级+块级匹配":e?"行级匹配":n?"块级匹配":"无匹配",r.matchedLines=o.size,r.aiCodePortion=s.changedLines>0?o.size/s.changedLines:0,console.log(`  📊 文件 ${s.fileName} 分析结果:`),console.log(`    - 总匹配行数: ${r.matchedLines}`),console.log(`    - AI代码占比: ${(100*r.aiCodePortion).toFixed(2)}%`),console.log(`    - 匹配详情: ${r.aiMatchDetails.length} 条`)}else console.log("  ⚠️  没有找到相关的AI生成内容，跳过比对"),r.detectionMethod="无AI记录";n.push(r),console.log(`  ✅ 文件 ${s.fileName} 分析完成`)}return console.log(`\n📊 代码比对分析完成，共分析 ${n.length} 个文件`),n}(D,C),H=Date.now()-N;console.log(`✅ 步骤3完成: 分析了 ${x.length} 个文件 (耗时: ${H}ms)`),console.log("\n📊 步骤4: 生成最终报告...");const M=Date.now(),P=function(o,e,n,t,s,l){let c=0,i=0,r=0;for(const $ of o)c+=$.totalLines,i+=$.changedLines,r+=$.matchedLines;const g=i>0?r/i:0,a=new Set;for(const $ of o)$.detectionMethod&&a.add($.detectionMethod);let f;if(0===a.size)f="无检测";else if(1===a.size)f=Array.from(a)[0];else{const o=Array.from(a).filter((o=>"无AI记录"!==o&&"无匹配"!==o));f=0===o.length?"无匹配":o.join("+")}return{pr_global_id:e,pr_title:n,git_repository:s,git_branch:l,analysis_timestamp:(new Date).toISOString(),mis_number:t||"N/A",detection_method:f,summary:{total_files:o.length,total_lines:c,changed_lines:i,ai_generated_lines:r,ai_code_ratio:g},file_details:o.map((o=>({file_name:o.fileName,file_type:o.fileType,total_lines:o.totalLines,changed_lines:o.changedLines,ai_matched_lines:o.matchedLines,ai_code_ratio:o.aiCodePortion,detection_method:o.detectionMethod||"未知"})))}}(x,h,_,m,y,w),T=Date.now()-M;console.log(`✅ 步骤4完成: 报告生成完毕 (耗时: ${T}ms)`);const G=Date.now()-r;console.log(`\n🎉 分析完成! 总耗时: ${G}ms`),function(o){console.log("\n"+"=".repeat(80)),console.log("🎯 AI代码分析最终报告"),console.log("=".repeat(80)),console.log("\n📋 基本信息:"),console.log(`  PR编号: ${o.pr_global_id}`),console.log(`  PR标题: ${o.pr_title}`),console.log(`  仓库: ${o.git_repository}`),console.log(`  分支: ${o.git_branch}`),console.log(`  MIS号: ${o.mis_number}`),console.log(`  检测方法: ${o.detection_method}`),console.log(`  提交日期: ${o.analysis_timestamp}`),console.log("\n📊 总体统计:"),console.log(`  总文件数: ${o.summary.total_files}`),console.log(`  总行数: ${o.summary.total_lines}`),console.log(`  变更行数: ${o.summary.changed_lines}`),console.log(`  AI生成行数: ${o.summary.ai_generated_lines}`),console.log(`  AI代码占比: ${(100*o.summary.ai_code_ratio).toFixed(2)}%`),console.log("\n📁 文件详情:"),o.file_details.forEach(((o,e)=>{console.log(`  ${e+1}. ${o.file_name}`),console.log(`     文件类型: ${o.file_type}`),console.log(`     总行数: ${o.total_lines}, 变更行数: ${o.changed_lines}`),console.log(`     AI匹配行数: ${o.ai_matched_lines}, AI占比: ${(100*o.ai_code_ratio).toFixed(2)}%`),console.log(`     检测方法: ${o.detection_method}`)})),console.log("\n"+"=".repeat(80)),console.log("✅ 报告输出完成"),console.log("=".repeat(80)),console.log("上报结果：",JSON.stringify(o,null,2))}(P),await async function(e){try{console.log("\n💾 开始存储分析结果到数据库...");const n="https://faas-common.inf.test.sankuai.com/api/function/node/PQrWBqKlcMcLOv4c",t={pr_global_id:e.pr_global_id,pr_title:e.pr_title,git_repository:e.git_repository,git_branch:e.git_branch,mis_number:e.mis_number,detection_method:e.detection_method,analysis_timestamp:e.analysis_timestamp,total_files:e.summary.total_files,total_lines:e.summary.total_lines,changed_lines:e.summary.changed_lines,ai_generated_lines:e.summary.ai_generated_lines,ai_code_ratio:e.summary.ai_code_ratio,file_details:e.file_details};console.log("📤 准备发送数据到API..."),console.log(`🌐 API地址: ${n}`),console.log("📊 数据概览:"),console.log(`  - PR ID: ${t.pr_global_id}`),console.log(`  - 总文件数: ${t.total_files}`),console.log(`  - AI代码占比: ${(100*t.ai_code_ratio).toFixed(2)}%`);const s=Date.now(),l=await o.post(n,t,{headers:{"Content-Type":"application/json"},timeout:3e4}),c=Date.now()-s;console.log(`✅ 数据库存储成功! (耗时: ${c}ms)`),console.log(`📊 响应状态: ${l.status}`),l.data&&(console.log("📋 存储响应:"),l.data.success?(console.log(`  ✅ ${l.data.message}`),l.data.data&&l.data.data.id&&console.log(`  🆔 报告ID: ${l.data.data.id}`)):console.log(`  ⚠️  ${l.data.message}`))}catch(n){console.error("❌ 存储分析结果到数据库失败:",n.message),n.response?(console.error(`📊 HTTP状态码: ${n.response.status}`),console.error("📋 错误响应:",n.response.data),409===n.response.status?console.log("💡 提示: PR ID已存在，这可能是重复提交"):400===n.response.status&&console.log("💡 提示: 请求参数有误，请检查数据格式")):"ECONNABORTED"===n.code?console.error("⏰ 请求超时，请检查网络连接"):console.error("🔗 网络连接失败，请检查API地址和网络状态"),console.log("⚠️  数据库存储失败，但分析流程继续完成")}}(P)}catch(d){throw console.error("❌ 分析过程出错:",d),console.error("错误堆栈:",d.stack),d}}()}));
