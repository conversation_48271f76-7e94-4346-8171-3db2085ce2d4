{"pr_global_id": "13073502", "pr_title": "【不要合并，只是测试webhook】", "git_repository": "ssh://*******************/dzfe/medical-home-page.git", "git_branch": "feature/fedo-213908", "analysis_timestamp": "2025-05-24T05:28:11.252Z", "summary": {"total_files": 826, "total_lines": 40383, "changed_lines": 40383, "ai_generated_lines": 228, "ai_code_ratio": 0.005645940123319219}, "file_details": [{"file_name": ".stylelintrc.js", "file_type": "js", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "babel.config.js", "file_type": "js", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "build.js", "file_type": "js", "total_lines": 32, "changed_lines": 32, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "index.tsx", "file_type": "tsx", "total_lines": 29, "changed_lines": 29, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "mrn.config.js", "file_type": "js", "total_lines": 74, "changed_lines": 74, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "preloadedModules.js", "file_type": "js", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/App.tsx", "file_type": "tsx", "total_lines": 32, "changed_lines": 32, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/communication/index.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/communication/knb/channels.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/communication/knb/eventConfig.ts", "file_type": "ts", "total_lines": 71, "changed_lines": 71, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/communication/knb/index.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/communication/knb/types.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/APIs/MRN_GET_V1_getabinfo_bin.ts", "file_type": "ts", "total_lines": 56, "changed_lines": 56, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/Models/CommonABResponse.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/Models/__CommonABResponse.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/hooks/useABData.ts", "file_type": "ts", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeAB/index.tsx", "file_type": "tsx", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/APIs/MRN_GET_V1_navigation_bin.ts", "file_type": "ts", "total_lines": 67, "changed_lines": 67, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Components/General/GeneralKingKong.tsx", "file_type": "tsx", "total_lines": 265, "changed_lines": 265, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Components/General/KingKongGrid.tsx", "file_type": "tsx", "total_lines": 42, "changed_lines": 42, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Components/General/KingKongPageIndicator.tsx", "file_type": "tsx", "total_lines": 70, "changed_lines": 70, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/CategoryBlockVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/CategoryItemVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/CategoryNavigationVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/DzchannelDefaultOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/ExpandVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/MoreLinkVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/NavigationBlockModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/NavigationBlockVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/NavigationModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/NavigationPositionRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/NavigationResponseVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/PutAwayVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/RecommendReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/ShowStyleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__CategoryBlockVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__CategoryItemVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__CategoryNavigationVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__DzchannelDefaultOceanVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__ExpandVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__MoreLinkVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__NavigationBlockModuleVO.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__NavigationBlockVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__NavigationModuleVO.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__NavigationPositionRespVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__NavigationResponseVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__PutAwayVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__RecommendReportInfo.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/Models/__ShowStyleVO.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/constants/bizCode.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/constants/kingKongLayoutConstants.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/constants/kingKongTypes.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/hooks/useCalculateSplitScreenInfo.ts", "file_type": "ts", "total_lines": 122, "changed_lines": 122, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/hooks/useKingKongData.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/hooks/useKingKongLX.ts", "file_type": "ts", "total_lines": 132, "changed_lines": 132, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeKingKong/index.tsx", "file_type": "tsx", "total_lines": 39, "changed_lines": 39, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/BaseList/PPBaseListModule.tsx", "file_type": "tsx", "total_lines": 141, "changed_lines": 141, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/BaseList/PPChannelBaseListModule.tsx", "file_type": "tsx", "total_lines": 165, "changed_lines": 165, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/BaseList/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/ComponentRegistry/builder.tsx", "file_type": "tsx", "total_lines": 101, "changed_lines": 101, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/ComponentRegistry/index.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/ComponentRegistry/registry.ts", "file_type": "ts", "total_lines": 62, "changed_lines": 62, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/ComponentRegistry/types.ts", "file_type": "ts", "total_lines": 129, "changed_lines": 129, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/ComponentRegistry/utils.ts", "file_type": "ts", "total_lines": 100, "changed_lines": 100, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/AdTag/AdTag.tsx", "file_type": "tsx", "total_lines": 54, "changed_lines": 54, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/AdTag/styles.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/BrandDeals/BrandDeals.tsx", "file_type": "tsx", "total_lines": 180, "changed_lines": 180, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/ActivityComponent/DefaultActivityComponent.tsx", "file_type": "tsx", "total_lines": 74, "changed_lines": 74, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/ActivityComponent/DefaultPromoBg.tsx", "file_type": "tsx", "total_lines": 39, "changed_lines": 39, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/ActivityComponent/ReductionActivityComponent.tsx", "file_type": "tsx", "total_lines": 97, "changed_lines": 97, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/ActivityComponent/SecKillComponent.tsx", "file_type": "tsx", "total_lines": 80, "changed_lines": 80, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/ActivityComponent/index.tsx", "file_type": "tsx", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/enum.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/index.tsx", "file_type": "tsx", "total_lines": 105, "changed_lines": 105, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/styles.ts", "file_type": "ts", "total_lines": 164, "changed_lines": 164, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CarPetHanInfo/utils.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Carousel/Carousel.tsx", "file_type": "tsx", "total_lines": 200, "changed_lines": 200, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ChannelTags/ChannelTag.tsx", "file_type": "tsx", "total_lines": 118, "changed_lines": 118, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ChannelTags/ChannelTags.tsx", "file_type": "tsx", "total_lines": 64, "changed_lines": 64, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ChannelTags/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ChannelTags/styles.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Common/styles/default.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Common/styles/theme.tsx", "file_type": "tsx", "total_lines": 76, "changed_lines": 76, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Common/utils/AppInfo.ts", "file_type": "ts", "total_lines": 82, "changed_lines": 82, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonTag/CommonTag.tsx", "file_type": "tsx", "total_lines": 693, "changed_lines": 693, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonTag/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonTag/styles.ts", "file_type": "ts", "total_lines": 809, "changed_lines": 809, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonText/CommonText.tsx", "file_type": "tsx", "total_lines": 198, "changed_lines": 198, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonText/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/CommonText/styles.ts", "file_type": "ts", "total_lines": 146, "changed_lines": 146, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/GradientColorVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/PromoDetailVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/PromoItemVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/PromoLabelVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/TagAttrVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__GradientColorVO.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 91, "changed_lines": 91, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__PromoDetailVO.ts", "file_type": "ts", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__PromoItemVO.ts", "file_type": "ts", "total_lines": 46, "changed_lines": 46, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__PromoLabelVO.ts", "file_type": "ts", "total_lines": 29, "changed_lines": 29, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/Models/__TagAttrVO.ts", "file_type": "ts", "total_lines": 89, "changed_lines": 89, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/index.tsx", "file_type": "tsx", "total_lines": 201, "changed_lines": 201, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/DiscountTag/styles.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Divider/Divider.tsx", "file_type": "tsx", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Divider/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Divider/styles.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/FeedHeadpic/FeedHeadPic.tsx", "file_type": "tsx", "total_lines": 226, "changed_lines": 226, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/FeedHeadpic/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/FeedHeadpic/styles.ts", "file_type": "ts", "total_lines": 92, "changed_lines": 92, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/FeedHeadpic/utils.ts", "file_type": "ts", "total_lines": 31, "changed_lines": 31, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/ActivityComponent/DefaultActivityComponent.tsx", "file_type": "tsx", "total_lines": 74, "changed_lines": 74, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/ActivityComponent/DefaultPromoBg.tsx", "file_type": "tsx", "total_lines": 39, "changed_lines": 39, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/ActivityComponent/ReductionActivityComponent.tsx", "file_type": "tsx", "total_lines": 97, "changed_lines": 97, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/ActivityComponent/SecKillComponent.tsx", "file_type": "tsx", "total_lines": 80, "changed_lines": 80, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/ActivityComponent/index.tsx", "file_type": "tsx", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/HanInfo.tsx", "file_type": "tsx", "total_lines": 197, "changed_lines": 197, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/enum.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/styles.ts", "file_type": "ts", "total_lines": 247, "changed_lines": 247, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/HanInfo/utils.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/Headpic.tsx", "file_type": "tsx", "total_lines": 267, "changed_lines": 267, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInner.tsx", "file_type": "tsx", "total_lines": 421, "changed_lines": 421, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInterestAtmos/InterestAtmos.tsx", "file_type": "tsx", "total_lines": 286, "changed_lines": 286, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInterestAtmos/InterestAtmosWrapper.tsx", "file_type": "tsx", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInterestAtmos/SecKillComponent.tsx", "file_type": "tsx", "total_lines": 47, "changed_lines": 47, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInterestAtmos/styles.ts", "file_type": "ts", "total_lines": 82, "changed_lines": 82, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/HeadpicInterestAtmos/utils.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/index.tsx", "file_type": "tsx", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Headpic/styles.ts", "file_type": "ts", "total_lines": 171, "changed_lines": 171, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/PoiLineInfo/PoiLineInfo.tsx", "file_type": "tsx", "total_lines": 94, "changed_lines": 94, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/PoiLineInfo/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/PoiLineInfo/styles.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Recommend/Recommend.tsx", "file_type": "tsx", "total_lines": 85, "changed_lines": 85, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Recommend/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Recommend/styles.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/Recommend/utils.ts", "file_type": "ts", "total_lines": 65, "changed_lines": 65, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/RecommendBottom/RecommendBottom.tsx", "file_type": "tsx", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/RecommendBottom/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/RecommendBottom/styles.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScoreInfo/NewStorePresale.tsx", "file_type": "tsx", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScoreInfo/ScoreInfo.tsx", "file_type": "tsx", "total_lines": 159, "changed_lines": 159, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScoreInfo/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScoreInfo/styles.ts", "file_type": "ts", "total_lines": 93, "changed_lines": 93, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScoreInfo/utils.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScrollMore/CommonAttachTriggerView.tsx", "file_type": "tsx", "total_lines": 37, "changed_lines": 37, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ScrollMore/GravelScrollMore.tsx", "file_type": "tsx", "total_lines": 215, "changed_lines": 215, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ShopHanInfo/ShopHanInfo.tsx", "file_type": "tsx", "total_lines": 92, "changed_lines": 92, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ShopHanInfo/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/ShopHanInfo/styles.ts", "file_type": "ts", "total_lines": 97, "changed_lines": 97, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/TitleComponent/TitleComponent.tsx", "file_type": "tsx", "total_lines": 93, "changed_lines": 93, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/TitleComponent/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/TitleComponent/styles.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/UserInfo/UserInfo.tsx", "file_type": "tsx", "total_lines": 97, "changed_lines": 97, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/UserInfo/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/atoms/UserInfo/style.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/Banner/index.tsx", "file_type": "tsx", "total_lines": 113, "changed_lines": 113, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/Banner/style.ts", "file_type": "ts", "total_lines": 54, "changed_lines": 54, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/Banner/utils.ts", "file_type": "ts", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/DoctorCard/index.tsx", "file_type": "tsx", "total_lines": 447, "changed_lines": 447, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/DoctorCard/styles.ts", "file_type": "ts", "total_lines": 340, "changed_lines": 340, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/FeedsProductCard/index.tsx", "file_type": "tsx", "total_lines": 200, "changed_lines": 200, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/FeedsProductCard/styles.ts", "file_type": "ts", "total_lines": 143, "changed_lines": 143, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/RBCard/RBCard.tsx", "file_type": "tsx", "total_lines": 228, "changed_lines": 228, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/RBCard/index.tsx", "file_type": "tsx", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/RBCard/styles.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/RBCard/utils.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/RBCard/verticalStyles.ts", "file_type": "ts", "total_lines": 65, "changed_lines": 65, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/ShopCard/index.tsx", "file_type": "tsx", "total_lines": 126, "changed_lines": 126, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/cards/ShopCard/styles.ts", "file_type": "ts", "total_lines": 56, "changed_lines": 56, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/constant.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/templates/FeedCardTemp/FeedCardTemp.tsx", "file_type": "tsx", "total_lines": 99, "changed_lines": 99, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/templates/FeedCardTemp/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/templates/FeedCardTemp/styles.ts", "file_type": "ts", "total_lines": 20, "changed_lines": 20, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/templates/ShopCardTemp/index.tsx", "file_type": "tsx", "total_lines": 130, "changed_lines": 130, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/templates/ShopCardTemp/styles.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/utils/appInfo.ts", "file_type": "ts", "total_lines": 82, "changed_lines": 82, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/utils/buildComponent.tsx", "file_type": "tsx", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/utils/index.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/utils/styles.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Components/utils/useSlot.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Configs/constant.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Configs/index.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Configs/main/cardComponentRegister.ts", "file_type": "ts", "total_lines": 52, "changed_lines": 52, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Configs/main/cardTemplateRegister.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/index.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/init.ts", "file_type": "ts", "total_lines": 77, "changed_lines": 77, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/type.ts", "file_type": "ts", "total_lines": 83, "changed_lines": 83, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/useListModule.ts", "file_type": "ts", "total_lines": 38, "changed_lines": 38, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/utils/extractDataCenterExtraInfo.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/utils/extractExtraInfo.ts", "file_type": "ts", "total_lines": 31, "changed_lines": 31, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/utils/extractPageProps.ts", "file_type": "ts", "total_lines": 28, "changed_lines": 28, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/utils/index.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/references/utils/initListVideoPreloader.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/store/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/store/selectors.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/store/store.tsx", "file_type": "tsx", "total_lines": 73, "changed_lines": 73, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/DataCenter/store/type.ts", "file_type": "ts", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/index.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useAddressChange.ts", "file_type": "ts", "total_lines": 31, "changed_lines": 31, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useAquireCouponRefresh.ts", "file_type": "ts", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useCityChange.ts", "file_type": "ts", "total_lines": 31, "changed_lines": 31, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useLoginRefresh.ts", "file_type": "ts", "total_lines": 43, "changed_lines": 43, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useMagicalCouponRefresh.ts", "file_type": "ts", "total_lines": 84, "changed_lines": 84, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useTabChange.ts", "file_type": "ts", "total_lines": 132, "changed_lines": 132, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useTabHover.ts", "file_type": "ts", "total_lines": 43, "changed_lines": 43, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/listeners/useTabSync.ts", "file_type": "ts", "total_lines": 119, "changed_lines": 119, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/macroListeners/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/macroListeners/useChannelListeners.ts", "file_type": "ts", "total_lines": 23, "changed_lines": 23, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/macroListeners/useFilterListeners.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/EventCenter/type.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/index.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useCacheList.ts", "file_type": "ts", "total_lines": 55, "changed_lines": 55, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useExtraInfo.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useGCRouse.ts", "file_type": "ts", "total_lines": 163, "changed_lines": 163, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useItemHeightEstimate.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useListItemEvents.ts", "file_type": "ts", "total_lines": 64, "changed_lines": 64, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useListUpdate.ts", "file_type": "ts", "total_lines": 134, "changed_lines": 134, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/usePartialRefresh.ts", "file_type": "ts", "total_lines": 222, "changed_lines": 222, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/usePreNetwork.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useScrollHandler.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useStopRequest.ts", "file_type": "ts", "total_lines": 49, "changed_lines": 49, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useTopComponentProps.ts", "file_type": "ts", "total_lines": 21, "changed_lines": 21, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Hooks/useVisibleItems.ts", "file_type": "ts", "total_lines": 96, "changed_lines": 96, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/index.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/index.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelAdClick.ts", "file_type": "ts", "total_lines": 40, "changed_lines": 40, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelAdExpose.ts", "file_type": "ts", "total_lines": 46, "changed_lines": 46, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelDealClick.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelDealExpose.ts", "file_type": "ts", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelHeadpicExpose.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelItemClick.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelItemExpose.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelRecommendItemClick.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelRecommendItemExpose.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processChannelRecommendMapi.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processFilterAdClick.ts", "file_type": "ts", "total_lines": 24, "changed_lines": 24, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processFilterAdExpose.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processFilterDealClick.ts", "file_type": "ts", "total_lines": 21, "changed_lines": 21, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processFilterItemClick.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/processors/processFilterItemExpose.ts", "file_type": "ts", "total_lines": 67, "changed_lines": 67, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/strategy/channelMGEStrategy.ts", "file_type": "ts", "total_lines": 57, "changed_lines": 57, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/strategy/filterMGEStrategy.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/strategy/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/type.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/utils/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/utils/processChannel.ts", "file_type": "ts", "total_lines": 354, "changed_lines": 354, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/MGE/utils/processFilter.ts", "file_type": "ts", "total_lines": 259, "changed_lines": 259, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/AdInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyCityResourceCardInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyCityResourceDetailInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyCityResourceShopInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyCityResourceTradeAttrs.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyWeatherResponseVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/BabyWeathersResponseVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/CarouselInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ChannelPageListRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ChannelPullStreamVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ChannelTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/CommonFilterItemInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/CommonFilterResponseVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/CraftsManCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/DealCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/DzChannelFloatTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/GradientColorVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ListCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/LiveCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PFLAdReportItemInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PFLChReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PFLWhaleReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PicInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PromoDetailVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PromoItemVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/PromoLabelVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecomListCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecomListContentCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecomListShopCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecommedWorksVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecommendReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RecommendWorksVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/RoundCornerRadius.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ShopBaseInformation.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/ShopCircumstanceVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/SimpleOperationCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/SubsidyProductCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TagAttrVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TagIconsVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TagTextVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TimeLimitCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TimeLimitCouponVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TimeLimitDealTypeVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TopicCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/TradeAttrVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/VideoCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/VideoInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__AdInfoVO.ts", "file_type": "ts", "total_lines": 37, "changed_lines": 37, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyCityResourceCardInfoVO.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyCityResourceDetailInfo.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyCityResourceShopInfo.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyCityResourceTradeAttrs.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyWeatherResponseVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__BabyWeathersResponseVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__CarouselInfoVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ChannelPageListRespVO.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ChannelPullStreamVO.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ChannelTagVO.ts", "file_type": "ts", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__CommonFilterItemInfoVO.ts", "file_type": "ts", "total_lines": 32, "changed_lines": 32, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__CommonFilterResponseVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__CraftsManCardVO.ts", "file_type": "ts", "total_lines": 48, "changed_lines": 48, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__DealCardVO.ts", "file_type": "ts", "total_lines": 91, "changed_lines": 91, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__DzChannelFloatTagVO.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__GradientColorVO.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ListCardVO.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__LiveCardVO.ts", "file_type": "ts", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PFLAdReportItemInfo.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PFLChReportInfo.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PFLWhaleReportInfo.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PicInfoVO.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PromoDetailVO.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PromoItemVO.ts", "file_type": "ts", "total_lines": 20, "changed_lines": 20, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__PromoLabelVO.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecomListCardVO.ts", "file_type": "ts", "total_lines": 56, "changed_lines": 56, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecomListContentCardVO.ts", "file_type": "ts", "total_lines": 84, "changed_lines": 84, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecomListShopCardVO.ts", "file_type": "ts", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecommedWorksVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecommendReportInfo.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RecommendWorksVO.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__RoundCornerRadius.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ShopBaseInformation.ts", "file_type": "ts", "total_lines": 86, "changed_lines": 86, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__ShopCircumstanceVO.ts", "file_type": "ts", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__SimpleOperationCardVO.ts", "file_type": "ts", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__SubsidyProductCardVO.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TagAttrVO.ts", "file_type": "ts", "total_lines": 48, "changed_lines": 48, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TagIconsVO.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TagTextVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TimeLimitCardVO.ts", "file_type": "ts", "total_lines": 23, "changed_lines": 23, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TimeLimitCouponVO.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TimeLimitDealTypeVO.ts", "file_type": "ts", "total_lines": 28, "changed_lines": 28, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TopicCardVO.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__TradeAttrVO.ts", "file_type": "ts", "total_lines": 47, "changed_lines": 47, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__VideoCardVO.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Models/__VideoInfoVO.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/TSApis/v1_dzchannelpagelist_bin.ts", "file_type": "ts", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/TSApis/v1_listingpagelist_bin.ts", "file_type": "ts", "total_lines": 54, "changed_lines": 54, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/ViewModel/factory.ts", "file_type": "ts", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/ViewModel/index.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/ViewModel/network.ts", "file_type": "ts", "total_lines": 126, "changed_lines": 126, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/ViewModel/viewModel.ts", "file_type": "ts", "total_lines": 189, "changed_lines": 189, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/config/channelRequest.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/config/filterRequest.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/config/index.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/config/type.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/index.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/prenetwork/ShopPreNetWorkDelegate.ts", "file_type": "ts", "total_lines": 426, "changed_lines": 426, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/prenetwork/ShopPreNetWorkStrategy4List.ts", "file_type": "ts", "total_lines": 76, "changed_lines": 76, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/prenetwork/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/utils/address/index.ts", "file_type": "ts", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Request/utils/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/decorator.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/getCommonValLabs.ts", "file_type": "ts", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/getNetworkTypeWithToken.ts", "file_type": "ts", "total_lines": 21, "changed_lines": 21, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/index.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/isLivePlayerSupport.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/isVideoSupport.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/log.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/rouseDotFilter.ts", "file_type": "ts", "total_lines": 65, "changed_lines": 65, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/rouseIntentWorker.tsx", "file_type": "tsx", "total_lines": 43, "changed_lines": 43, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Utils/supportRouseIntent.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonBGView.tsx", "file_type": "tsx", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonEmptyView.tsx", "file_type": "tsx", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonFooterView.tsx", "file_type": "tsx", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonHeaderView.tsx", "file_type": "tsx", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonItemView.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonLoadingMoreView.tsx", "file_type": "tsx", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/CommonLoadingView.tsx", "file_type": "tsx", "total_lines": 64, "changed_lines": 64, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/index.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/Views/type.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/index.tsx", "file_type": "tsx", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeList/type.ts", "file_type": "ts", "total_lines": 38, "changed_lines": 38, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/APIs/MRN_GET_Common_operations_bin.ts", "file_type": "ts", "total_lines": 66, "changed_lines": 66, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Components/MarketEntranceWidget.tsx", "file_type": "tsx", "total_lines": 245, "changed_lines": 245, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelColumnVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelLiveCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelOperationButtonVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelOperationContentVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelProductPriceVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelProductShopVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelProductVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelPullStreamVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelShopAddressVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelShopVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ChannelTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzBookCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzBookInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzBookProjectVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzChannelOperationCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzChannelOperationRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzCouponPackageVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzOperationCouponVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzPictureVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/DzTextDisplayVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/FloatTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/GameCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/GradientColorVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/HostsVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ItemCardVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/OperationTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/OperationTitleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ProductPictureVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/ProjectDescInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/RecommendReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/RoundCornerRadius.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/SummaryInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/TagAttrVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/TagIconsVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/TagTextVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/TitleDescVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelColumnVO.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelLiveCardVO.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelOperationButtonVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelOperationContentVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelProductPriceVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelProductShopVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelProductVO.ts", "file_type": "ts", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelPullStreamVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelShopAddressVO.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelShopVO.ts", "file_type": "ts", "total_lines": 37, "changed_lines": 37, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ChannelTagVO.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzBookCardVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzBookInfoVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzBookProjectVO.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzChannelOperationCardVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzChannelOperationRespVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzCouponPackageVO.ts", "file_type": "ts", "total_lines": 23, "changed_lines": 23, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzOperationCouponVO.ts", "file_type": "ts", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzPictureVO.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__DzTextDisplayVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__FloatTagVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__GameCardVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__GradientColorVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__HostsVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ItemCardVO.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ListBaseFieldVO.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__MagicalMemberCouponItem.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__OperationTagVO.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__OperationTitleVO.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ProductPictureVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__ProjectDescInfo.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__RecommendReportInfo.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__RoundCornerRadius.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__SummaryInfoVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__TagAttrVO.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__TagIconsVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__TagTextVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/Models/__TitleDescVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/hooks/useMarketEntranceData.ts", "file_type": "ts", "total_lines": 50, "changed_lines": 50, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/hooks/useMarketEntranceLX.ts", "file_type": "ts", "total_lines": 68, "changed_lines": 68, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeMarketEntrance/index.tsx", "file_type": "tsx", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/APIs/MRN_GET_V1_floatinglayer_bin.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Components/SideFloat.tsx", "file_type": "tsx", "total_lines": 157, "changed_lines": 157, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/DzFloatLayerOceanReportVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/DzFloatLayerRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/DzFloatingLayerCommonVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/__DzFloatLayerOceanReportVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/__DzFloatLayerRespVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/__DzFloatingLayerCommonVO.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/Models/__PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/hooks/useMgeInfo.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/hooks/useSideFloatData.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/hooks/useStyleInfo.ts", "file_type": "ts", "total_lines": 22, "changed_lines": 22, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeSideFloat/index.tsx", "file_type": "tsx", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/APIs/MRN_GET_V1_commonfilter_bin.ts", "file_type": "ts", "total_lines": 87, "changed_lines": 87, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/AtomComponent/CommonPicker.tsx", "file_type": "tsx", "total_lines": 240, "changed_lines": 240, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BaseTab/BaseCommonTabItem/index.tsx", "file_type": "tsx", "total_lines": 45, "changed_lines": 45, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BaseTab/BaseCommonTabItem/styles.ts", "file_type": "ts", "total_lines": 29, "changed_lines": 29, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BaseTab/BaseScrollTabCell/BaseScrollTabCell.tsx", "file_type": "tsx", "total_lines": 592, "changed_lines": 592, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BaseTab/BaseScrollTabCell/interface.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BaseTab/BaseScrollTabCell/styles.ts", "file_type": "ts", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BizCommonTab/CommonPullDownTabItem/index.tsx", "file_type": "tsx", "total_lines": 465, "changed_lines": 465, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BizCommonTab/CommonPullDownTabItem/interface.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BizCommonTab/CommonPullDownTabItem/styles.ts", "file_type": "ts", "total_lines": 92, "changed_lines": 92, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BizCommonTab/CommonUnderlineTabItem/index.tsx", "file_type": "tsx", "total_lines": 192, "changed_lines": 192, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/BaseComponent/BizCommonTab/CommonUnderlineTabItem/styles.ts", "file_type": "ts", "total_lines": 85, "changed_lines": 85, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardPicker/Util.ts", "file_type": "ts", "total_lines": 68, "changed_lines": 68, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardPicker/index.tsx", "file_type": "tsx", "total_lines": 137, "changed_lines": 137, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardPicker/interface.ts", "file_type": "ts", "total_lines": 70, "changed_lines": 70, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardPicker/styles.ts", "file_type": "ts", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelect/StandardSelectItem/index.tsx", "file_type": "tsx", "total_lines": 160, "changed_lines": 160, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelect/StandardSelectItem/style.ts", "file_type": "ts", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelect/index.tsx", "file_type": "tsx", "total_lines": 164, "changed_lines": 164, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelect/style.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterLeftScroll/StandardFilterLeftScrollItem/index.tsx", "file_type": "tsx", "total_lines": 91, "changed_lines": 91, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterLeftScroll/StandardFilterLeftScrollItem/style.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterLeftScroll/index.tsx", "file_type": "tsx", "total_lines": 244, "changed_lines": 244, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterLeftScroll/style.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/GroupNormalItem/index.tsx", "file_type": "tsx", "total_lines": 236, "changed_lines": 236, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/GroupNormalItem/style.ts", "file_type": "ts", "total_lines": 41, "changed_lines": 41, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/GroupPriceRangeItem/index.tsx", "file_type": "tsx", "total_lines": 378, "changed_lines": 378, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/GroupPriceRangeItem/style.ts", "file_type": "ts", "total_lines": 58, "changed_lines": 58, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/Utils/PriceRangeUtil.tsx", "file_type": "tsx", "total_lines": 48, "changed_lines": 48, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/StandardFilterRightScrollItem/Utils/index.tsx", "file_type": "tsx", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/index.tsx", "file_type": "tsx", "total_lines": 264, "changed_lines": 264, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/StandardFilterRightScrollGroup/style.ts", "file_type": "ts", "total_lines": 57, "changed_lines": 57, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/index.tsx", "file_type": "tsx", "total_lines": 355, "changed_lines": 355, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/StandardFilterRightScroll/style.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/index.tsx", "file_type": "tsx", "total_lines": 358, "changed_lines": 358, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectFilter/style.ts", "file_type": "ts", "total_lines": 66, "changed_lines": 66, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectLeft/StandardSelectLeftItem/index.tsx", "file_type": "tsx", "total_lines": 254, "changed_lines": 254, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectLeft/StandardSelectLeftItem/style.ts", "file_type": "ts", "total_lines": 96, "changed_lines": 96, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectLeft/index.tsx", "file_type": "tsx", "total_lines": 308, "changed_lines": 308, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectLeft/style.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectTab/StandardSelectTabItem/index.tsx", "file_type": "tsx", "total_lines": 145, "changed_lines": 145, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectTab/StandardSelectTabItem/style.ts", "file_type": "ts", "total_lines": 24, "changed_lines": 24, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectTab/index.tsx", "file_type": "tsx", "total_lines": 218, "changed_lines": 218, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Base/StandardWidget/StandardSelectTab/style.ts", "file_type": "ts", "total_lines": 27, "changed_lines": 27, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/DPAddressBar/index.tsx", "file_type": "tsx", "total_lines": 108, "changed_lines": 108, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/DPAddressBar/style.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/MTAddressBar/Util.ts", "file_type": "ts", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/MTAddressBar/index.tsx", "file_type": "tsx", "total_lines": 87, "changed_lines": 87, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/MTAddressBar/style.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/Util.ts", "file_type": "ts", "total_lines": 67, "changed_lines": 67, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/index.tsx", "file_type": "tsx", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/interface.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/AddressDisplay/style.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/CategoryPickerPanel/index.tsx", "file_type": "tsx", "total_lines": 169, "changed_lines": 169, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/CategoryPickerPanel/style.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/GridTagPickerPanel/index.tsx", "file_type": "tsx", "total_lines": 293, "changed_lines": 293, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/GridTagPickerPanel/style.ts", "file_type": "ts", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/AddressLine/index.tsx", "file_type": "tsx", "total_lines": 147, "changed_lines": 147, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/AddressLine/style.ts", "file_type": "ts", "total_lines": 34, "changed_lines": 34, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/DistanceLine/DistanceItem/index.tsx", "file_type": "tsx", "total_lines": 139, "changed_lines": 139, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/DistanceLine/DistanceItem/style.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/DistanceLine/Util.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/DistanceLine/index.tsx", "file_type": "tsx", "total_lines": 81, "changed_lines": 81, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/DistanceLine/style.ts", "file_type": "ts", "total_lines": 12, "changed_lines": 12, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/SelectLeft/index.tsx", "file_type": "tsx", "total_lines": 336, "changed_lines": 336, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/SelectLeft/style.ts", "file_type": "ts", "total_lines": 21, "changed_lines": 21, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/index.tsx", "file_type": "tsx", "total_lines": 125, "changed_lines": 125, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Components/Business/Medical/NearbyAddressPickerPanel/style.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/Interfaces/NotifyListData.ts", "file_type": "ts", "total_lines": 72, "changed_lines": 72, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/Interfaces/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/NotifyAddress.ts", "file_type": "ts", "total_lines": 19, "changed_lines": 19, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/NotifyList.ts", "file_type": "ts", "total_lines": 204, "changed_lines": 204, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/Refresh.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/ToolTipDismiss.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/DataFlowHub/WhiteBoardKeys.ts", "file_type": "ts", "total_lines": 72, "changed_lines": 72, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/CommonFilterItemInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/CommonFilterResponseVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/FilterHeaderVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/PicFilterVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/RecommendReportInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/__CommonFilterItemInfoVO.ts", "file_type": "ts", "total_lines": 112, "changed_lines": 112, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/__CommonFilterResponseVO.ts", "file_type": "ts", "total_lines": 46, "changed_lines": 46, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/__FilterHeaderVO.ts", "file_type": "ts", "total_lines": 29, "changed_lines": 29, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/__PicFilterVO.ts", "file_type": "ts", "total_lines": 33, "changed_lines": 33, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/Models/__RecommendReportInfo.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/FilterNode.ts", "file_type": "ts", "total_lines": 102, "changed_lines": 102, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/NodeInterfaces/ITreeNode.ts", "file_type": "ts", "total_lines": 181, "changed_lines": 181, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/NodeTreeUtils/NodeTreeUtil.ts", "file_type": "ts", "total_lines": 220, "changed_lines": 220, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/NodeTreeUtils/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/TreeNode.ts", "file_type": "ts", "total_lines": 176, "changed_lines": 176, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/NodeTree/TreeSharedDataStore.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/index.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/medical/index.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/medical/tabConfig.ts", "file_type": "ts", "total_lines": 137, "changed_lines": 137, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/medical/tabNodeLogic.ts", "file_type": "ts", "total_lines": 310, "changed_lines": 310, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/medical/tabUIConfig.ts", "file_type": "ts", "total_lines": 320, "changed_lines": 320, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/medical/trackingConfig.ts", "file_type": "ts", "total_lines": 96, "changed_lines": 96, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/utils/FilterRecallparamjsonUtil.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/utils/OnAddressChangeUtil.ts", "file_type": "ts", "total_lines": 66, "changed_lines": 66, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/configs/utils/OnCategoryChangeUtil.ts", "file_type": "ts", "total_lines": 85, "changed_lines": 85, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/constants/tabEnums.ts", "file_type": "ts", "total_lines": 42, "changed_lines": 42, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigCanvas.tsx", "file_type": "tsx", "total_lines": 330, "changed_lines": 330, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/IConfigCanvas.ts", "file_type": "ts", "total_lines": 58, "changed_lines": 58, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/IConfigFile.ts", "file_type": "ts", "total_lines": 246, "changed_lines": 246, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/IConfigLayout.ts", "file_type": "ts", "total_lines": 29, "changed_lines": 29, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/IConfigParser.ts", "file_type": "ts", "total_lines": 287, "changed_lines": 287, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/IConfigStyle.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigInterfaces/index.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigNodeTreeBind.ts", "file_type": "ts", "total_lines": 119, "changed_lines": 119, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigParser.tsx", "file_type": "tsx", "total_lines": 105, "changed_lines": 105, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigRegister/RegisterCell.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigRegister/RegisterCellItem.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/Debug/DebugTree.ts", "file_type": "ts", "total_lines": 32, "changed_lines": 32, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/ParserUtil.ts", "file_type": "ts", "total_lines": 189, "changed_lines": 189, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/RegisterCellItemUtil.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/RegisterCellUtil.ts", "file_type": "ts", "total_lines": 35, "changed_lines": 35, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/RegisterConfigUtil.ts", "file_type": "ts", "total_lines": 34, "changed_lines": 34, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/StyleUtil.ts", "file_type": "ts", "total_lines": 68, "changed_lines": 68, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigUtils/index.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigViews/ConfigCell.tsx", "file_type": "tsx", "total_lines": 183, "changed_lines": 183, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/core/ConfigViews/ConfigCellItem.tsx", "file_type": "tsx", "total_lines": 39, "changed_lines": 39, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/hooks/useBaseTabData.ts", "file_type": "ts", "total_lines": 158, "changed_lines": 158, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/index.tsx", "file_type": "tsx", "total_lines": 112, "changed_lines": 112, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/types/BaseMge.ts", "file_type": "ts", "total_lines": 45, "changed_lines": 45, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/types/MgeProps.ts", "file_type": "ts", "total_lines": 45, "changed_lines": 45, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/types/Request.ts", "file_type": "ts", "total_lines": 45, "changed_lines": 45, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/types/UIModel.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/types/index.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/AddressUtil.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/CacheUtil.ts", "file_type": "ts", "total_lines": 77, "changed_lines": 77, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/CheckUtil.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 6, "ai_code_ratio": 0.5454545454545454}, {"file_name": "src/components/business/HomeTab/utils/DataUtil.ts", "file_type": "ts", "total_lines": 17, "changed_lines": 17, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/ErrorUtil.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/EventBus.ts", "file_type": "ts", "total_lines": 39, "changed_lines": 39, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/MTFlexboxUtil.ts", "file_type": "ts", "total_lines": 100, "changed_lines": 100, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/MgeParamUtil.ts", "file_type": "ts", "total_lines": 52, "changed_lines": 52, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/ModelTransferUtil.ts", "file_type": "ts", "total_lines": 52, "changed_lines": 52, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/PickerDefaultLogicUtil.ts", "file_type": "ts", "total_lines": 99, "changed_lines": 99, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/RequestUtil.ts", "file_type": "ts", "total_lines": 132, "changed_lines": 132, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/ScrollToXUtil.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/ShenquanLogicUtil.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/SpecialPriceLogicUtil.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTab/utils/index.ts", "file_type": "ts", "total_lines": 21, "changed_lines": 21, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/APIs/MRN_GET_V1_theme_bin.ts", "file_type": "ts", "total_lines": 56, "changed_lines": 56, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Components/General/GeneralTheme.tsx", "file_type": "tsx", "total_lines": 40, "changed_lines": 40, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/ChannePicVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/DzChannelBgPic.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/DzChannelBgVideoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/DzThemeVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/PFLOceanPageInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__ChannePicVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__DzChannelBgPic.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__DzChannelBgVideoVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__DzThemeVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/Models/__PFLOceanPageInfo.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/constants/themeType.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/hooks/useThemeData.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTheme/index.tsx", "file_type": "tsx", "total_lines": 20, "changed_lines": 20, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/APIs/MRN_GET_V1_searchbox_bin.ts", "file_type": "ts", "total_lines": 67, "changed_lines": 67, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Components/GeneralTitleBarWidget.tsx", "file_type": "tsx", "total_lines": 255, "changed_lines": 255, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Components/LeftIconWidget.tsx", "file_type": "tsx", "total_lines": 109, "changed_lines": 109, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Components/RightIconWidget.tsx", "file_type": "tsx", "total_lines": 108, "changed_lines": 108, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Components/SearchBarWidget.tsx", "file_type": "tsx", "total_lines": 250, "changed_lines": 250, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Components/SwitchWidgetOpacityOnScroll.tsx", "file_type": "tsx", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/AppPlatformSearchBoxVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/CarouselModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/ChannelCitySelectItemVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/CitySelectModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzCarouselItemVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzChannelNavigationButtonVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzPictureVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxActionVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxAddress.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxAddressModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxAddressOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxBubbleModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxBubbleOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxBubbleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxCalendarVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxCommonModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxContentVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxModuleRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxRightIconVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxTabModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxTabOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxTabVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxUserDetail.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxUserOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzSearchBoxUsersModuleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/DzchannelDefaultOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/FloatTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/SearchBoxActivityVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/SearchTab.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/SearchView.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/TabSearchView.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__AppPlatformSearchBoxVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__CarouselModuleVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__ChannelCitySelectItemVO.ts", "file_type": "ts", "total_lines": 9, "changed_lines": 9, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__CitySelectModuleVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzCarouselItemVO.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzChannelNavigationButtonVO.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzPictureVO.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxActionVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxAddress.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxAddressModuleVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxAddressOceanVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxBubbleModuleVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxBubbleOceanVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxBubbleVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxCalendarVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxCommonModuleVO.ts", "file_type": "ts", "total_lines": 40, "changed_lines": 40, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxContentVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxModuleRespVO.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxOceanVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxRightIconVO.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxTabModuleVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxTabOceanVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxTabVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxUserDetail.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxUserOceanVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzSearchBoxUsersModuleVO.ts", "file_type": "ts", "total_lines": 15, "changed_lines": 15, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__DzchannelDefaultOceanVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__FloatTagVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__SearchBoxActivityVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__SearchTab.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__SearchView.ts", "file_type": "ts", "total_lines": 16, "changed_lines": 16, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/Models/__TabSearchView.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/constants/titleBarConstants.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/constants/titleBarEnums.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/hooks/useRightIconLX.ts", "file_type": "ts", "total_lines": 46, "changed_lines": 46, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/hooks/useSearchBarLX.ts", "file_type": "ts", "total_lines": 51, "changed_lines": 51, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/hooks/useSearchData.ts", "file_type": "ts", "total_lines": 42, "changed_lines": 42, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/index.tsx", "file_type": "tsx", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTitleBar/utils/titleBarUtils.ts", "file_type": "ts", "total_lines": 66, "changed_lines": 66, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/APIs/MRN_GET_V1_promotion_bin.ts", "file_type": "ts", "total_lines": 61, "changed_lines": 61, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Components/General/MainVenuePromotion.tsx", "file_type": "tsx", "total_lines": 58, "changed_lines": 58, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Components/General/OneToTwoPromotion.tsx", "file_type": "tsx", "total_lines": 91, "changed_lines": 91, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Components/General/SCurveWedget.tsx", "file_type": "tsx", "total_lines": 30, "changed_lines": 30, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/ActivityTitleVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DotInfoVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzBrandPromotionActivityMainVenue.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzBrandPromotionActivitySubVenue.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzBrandPromotionOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzBrandPromotionVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzGreatPromotionRespVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzPictureVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzPromotionActivityBanner.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzPromotionCommonOceanVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/DzPromotionCommonVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/FloatTagVO.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__ActivityTitleVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DotInfoVO.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzBrandPromotionActivityMainVenue.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzBrandPromotionActivitySubVenue.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzBrandPromotionOceanVO.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzBrandPromotionVO.ts", "file_type": "ts", "total_lines": 11, "changed_lines": 11, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzGreatPromotionRespVO.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzPictureVO.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzPromotionActivityBanner.ts", "file_type": "ts", "total_lines": 13, "changed_lines": 13, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzPromotionCommonOceanVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__DzPromotionCommonVO.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__FloatTagVO.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/Models/__PFLOceanItemInfo.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/constants/topPromotionConstants.ts", "file_type": "ts", "total_lines": 8, "changed_lines": 8, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/constants/topPromotionType.ts", "file_type": "ts", "total_lines": 7, "changed_lines": 7, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/hooks/useTopPromotionData.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/hooks/useTopPromotionLX.ts", "file_type": "ts", "total_lines": 88, "changed_lines": 88, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/components/business/HomeTopPromotion/index.tsx", "file_type": "tsx", "total_lines": 23, "changed_lines": 23, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/config/index.tsx", "file_type": "tsx", "total_lines": 20, "changed_lines": 20, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/config/pageConfigs/MedicalBeauty/MedicalBeautyConfig.ts", "file_type": "ts", "total_lines": 142, "changed_lines": 142, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/config/pageConfigs/MedicalBeauty/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/config/pageConfigs/MedicalMain/MedicalMainConfig.ts", "file_type": "ts", "total_lines": 126, "changed_lines": 126, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/config/pageConfigs/MedicalMain/index.ts", "file_type": "ts", "total_lines": 2, "changed_lines": 2, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/constants/appId.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/core/ChannelManager.ts", "file_type": "ts", "total_lines": 276, "changed_lines": 276, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/core/ChannelTypes.ts", "file_type": "ts", "total_lines": 93, "changed_lines": 93, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/core/MiddlewareSystem.ts", "file_type": "ts", "total_lines": 58, "changed_lines": 58, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/core/channelFactory.ts", "file_type": "ts", "total_lines": 105, "changed_lines": 105, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/core/index.ts", "file_type": "ts", "total_lines": 26, "changed_lines": 26, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/middleware/cycleDetectionMiddleware.ts", "file_type": "ts", "total_lines": 103, "changed_lines": 103, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/middleware/eventTracker.ts", "file_type": "ts", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/middleware/eventTrackingMiddleware.ts", "file_type": "ts", "total_lines": 103, "changed_lines": 103, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/middleware/index.ts", "file_type": "ts", "total_lines": 42, "changed_lines": 42, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/communication/middleware/knbMiddleware.ts", "file_type": "ts", "total_lines": 153, "changed_lines": 153, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/HomeContainer.tsx", "file_type": "tsx", "total_lines": 53, "changed_lines": 53, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/HomePageGeneralContainer.tsx", "file_type": "tsx", "total_lines": 134, "changed_lines": 134, "ai_matched_lines": 29, "ai_code_ratio": 0.21641791044776118}, {"file_name": "src/core/container/NewHomeBottomBarContainer.tsx", "file_type": "tsx", "total_lines": 227, "changed_lines": 227, "ai_matched_lines": 193, "ai_code_ratio": 0.8502202643171806}, {"file_name": "src/core/container/hooks/useInitListener.tsx", "file_type": "tsx", "total_lines": 40, "changed_lines": 40, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/model/BottomBarTypes.ts", "file_type": "ts", "total_lines": 48, "changed_lines": 48, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/AbnormalTabOverlay.tsx", "file_type": "tsx", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/ChannelSecondFloor/ChannelSecondFloor.tsx", "file_type": "tsx", "total_lines": 356, "changed_lines": 356, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/ChannelSecondFloor/ScrollDownFloorContainer.tsx", "file_type": "tsx", "total_lines": 163, "changed_lines": 163, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/NewBottomBarItemWidget.tsx", "file_type": "tsx", "total_lines": 111, "changed_lines": 111, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/NewBottomBarWidget.tsx", "file_type": "tsx", "total_lines": 71, "changed_lines": 71, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/container/widget/PageWebView.tsx", "file_type": "tsx", "total_lines": 73, "changed_lines": 73, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/hooks/useNavigationHandler.ts", "file_type": "ts", "total_lines": 32, "changed_lines": 32, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/navigation/routers.tsx", "file_type": "tsx", "total_lines": 55, "changed_lines": 55, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/core/services/requestContext.ts", "file_type": "ts", "total_lines": 178, "changed_lines": 178, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/index.ts", "file_type": "ts", "total_lines": 3, "changed_lines": 3, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useCacheData.ts", "file_type": "ts", "total_lines": 65, "changed_lines": 65, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useCommonParams.ts", "file_type": "ts", "total_lines": 34, "changed_lines": 34, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useInitCacheConfig.ts", "file_type": "ts", "total_lines": 31, "changed_lines": 31, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useLocation.ts", "file_type": "ts", "total_lines": 10, "changed_lines": 10, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useRequest.ts", "file_type": "ts", "total_lines": 97, "changed_lines": 97, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/hooks/useRequestParams.ts", "file_type": "ts", "total_lines": 14, "changed_lines": 14, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/routers.tsx", "file_type": "tsx", "total_lines": 48, "changed_lines": 48, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/stores/globalStore.ts", "file_type": "ts", "total_lines": 36, "changed_lines": 36, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/stores/index.ts", "file_type": "ts", "total_lines": 6, "changed_lines": 6, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/styles/global.scss", "file_type": "scss", "total_lines": 252, "changed_lines": 252, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/styles/index.ts", "file_type": "ts", "total_lines": 46, "changed_lines": 46, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/styles/variables.scss", "file_type": "scss", "total_lines": 47, "changed_lines": 47, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/types/context.ts", "file_type": "ts", "total_lines": 44, "changed_lines": 44, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/types/globalStore.ts", "file_type": "ts", "total_lines": 104, "changed_lines": 104, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/types/index.ts", "file_type": "ts", "total_lines": 4, "changed_lines": 4, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/typings.d.ts", "file_type": "ts", "total_lines": 37, "changed_lines": 37, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/DeviceUtils.ts", "file_type": "ts", "total_lines": 118, "changed_lines": 118, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/KNBUtils.ts", "file_type": "ts", "total_lines": 189, "changed_lines": 189, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/MRNStatisticsHooks.ts", "file_type": "ts", "total_lines": 102, "changed_lines": 102, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/MSIUtils.ts", "file_type": "ts", "total_lines": 34, "changed_lines": 34, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/OwlHelper.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/RequestDataCacheManager.ts", "file_type": "ts", "total_lines": 136, "changed_lines": 136, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/SafeAreaViewUtils.ts", "file_type": "ts", "total_lines": 25, "changed_lines": 25, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/TextCompatUtils.ts", "file_type": "ts", "total_lines": 62, "changed_lines": 62, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/TouchableOpacityUtils.ts", "file_type": "ts", "total_lines": 18, "changed_lines": 18, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/format.ts", "file_type": "ts", "total_lines": 60, "changed_lines": 60, "ai_matched_lines": 0, "ai_code_ratio": 0}, {"file_name": "src/utils/index.ts", "file_type": "ts", "total_lines": 5, "changed_lines": 5, "ai_matched_lines": 0, "ai_code_ratio": 0}]}