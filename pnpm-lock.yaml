lockfileVersion: "6.0"

settings:
    autoInstallPeers: true
    excludeLinksFromLockfile: false

dependencies:
    "@nibfe/elink-sdk-web":
        specifier: 1.0.10
        version: 1.0.10(axios@0.21.1)
    "@nibfe/white-screen-sdk":
        specifier: 0.2.4
        version: 0.2.4(rollup@2.79.2)
    esbuild:
        specifier: "0.8"
        version: 0.8.57
    html2canvas:
        specifier: 1.4.1
        version: 1.4.1

devDependencies:
    typescript:
        specifier: ^4.2.3
        version: 4.9.5
    vite:
        specifier: ^2.2.3
        version: 2.9.18

packages:
    /@ampproject/worker-dom@0.30.1:
        resolution:
            {
                integrity: sha1-qljll3DY4jRIdlBsuI3QUWpuP8I=,
                tarball: http://r.npm.sankuai.com/@ampproject/worker-dom/download/@ampproject/worker-dom-0.30.1.tgz,
            }
        engines: { node: ">=10.14" }
        dev: false

    /@bfe/logan-rtl-js-web@1.3.6:
        resolution:
            {
                integrity: sha1-uc7J6W7Nr7oQxgK+qd8l3iJ5YoQ=,
                tarball: http://r.npm.sankuai.com/@bfe/logan-rtl-js-web/download/@bfe/logan-rtl-js-web-1.3.6.tgz,
            }
        dev: false

    /@esbuild/linux-loong64@0.14.54:
        resolution:
            {
                integrity: sha1-3ipL5ni9TQ0f+7hubed5zeWZkCg=,
                tarball: http://r.npm.sankuai.com/@esbuild/linux-loong64/download/@esbuild/linux-loong64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [loong64]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /@nibfe/elink-sdk-web@1.0.10(axios@0.21.1):
        resolution:
            {
                integrity: sha1-RVK3mqkJbrv6bGH6DETgnFOeHzs=,
                tarball: http://r.npm.sankuai.com/@nibfe/elink-sdk-web/download/@nibfe/elink-sdk-web-1.0.10.tgz,
            }
        peerDependencies:
            axios: "*"
        dependencies:
            "@bfe/logan-rtl-js-web": 1.3.6
            axios: 0.21.1
        dev: false

    /@nibfe/white-screen-sdk@0.2.4(rollup@2.79.2):
        resolution:
            {
                integrity: sha1-xImkLc63ZTxDbFgtGpaOZYQ2R+s=,
                tarball: http://r.npm.sankuai.com/@nibfe/white-screen-sdk/download/@nibfe/white-screen-sdk-0.2.4.tgz,
            }
        dependencies:
            "@ampproject/worker-dom": 0.30.1
            "@rollup/plugin-json": 4.1.0(rollup@2.79.2)
            "@throne/html2canvas": 0.0.2-beta.0
            axios: 0.21.1
            jsdom: 16.7.0
        transitivePeerDependencies:
            - bufferutil
            - canvas
            - debug
            - rollup
            - supports-color
            - utf-8-validate
        dev: false

    /@rollup/plugin-json@4.1.0(rollup@2.79.2):
        resolution:
            {
                integrity: sha1-VOCYZ65pY8WThE2L16nHGClElvM=,
                tarball: http://r.npm.sankuai.com/@rollup/plugin-json/download/@rollup/plugin-json-4.1.0.tgz,
            }
        peerDependencies:
            rollup: ^1.20.0 || ^2.0.0
        dependencies:
            "@rollup/pluginutils": 3.1.0(rollup@2.79.2)
            rollup: 2.79.2
        dev: false

    /@rollup/pluginutils@3.1.0(rollup@2.79.2):
        resolution:
            {
                integrity: sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=,
                tarball: http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-3.1.0.tgz,
            }
        engines: { node: ">= 8.0.0" }
        peerDependencies:
            rollup: ^1.20.0||^2.0.0
        dependencies:
            "@types/estree": 0.0.39
            estree-walker: 1.0.1
            picomatch: 2.3.1
            rollup: 2.79.2
        dev: false

    /@throne/html2canvas@0.0.2-beta.0:
        resolution:
            {
                integrity: sha1-pHiMF1MPbRBeQWDfxSzVWD0JJL4=,
                tarball: http://r.npm.sankuai.com/@throne/html2canvas/download/@throne/html2canvas-0.0.2-beta.0.tgz,
            }
        engines: { node: ">=8.0.0" }
        dependencies:
            css-line-break: 2.0.1
            text-segmentation: 1.0.3
        dev: false

    /@tootallnate/once@1.1.2:
        resolution:
            {
                integrity: sha1-zLkURTYBeaBOf+av94wA/8Hur4I=,
                tarball: http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz,
            }
        engines: { node: ">= 6" }
        dev: false

    /@types/estree@0.0.39:
        resolution:
            {
                integrity: sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=,
                tarball: http://r.npm.sankuai.com/@types/estree/download/@types/estree-0.0.39.tgz,
            }
        dev: false

    /abab@2.0.6:
        resolution:
            {
                integrity: sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE=,
                tarball: http://r.npm.sankuai.com/abab/download/abab-2.0.6.tgz,
            }
        dev: false

    /acorn-globals@6.0.0:
        resolution:
            {
                integrity: sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=,
                tarball: http://r.npm.sankuai.com/acorn-globals/download/acorn-globals-6.0.0.tgz,
            }
        dependencies:
            acorn: 7.4.1
            acorn-walk: 7.2.0
        dev: false

    /acorn-walk@7.2.0:
        resolution:
            {
                integrity: sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=,
                tarball: http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-7.2.0.tgz,
            }
        engines: { node: ">=0.4.0" }
        dev: false

    /acorn@7.4.1:
        resolution:
            {
                integrity: sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=,
                tarball: http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz,
            }
        engines: { node: ">=0.4.0" }
        hasBin: true
        dev: false

    /acorn@8.14.1:
        resolution:
            {
                integrity: sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=,
                tarball: http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz,
            }
        engines: { node: ">=0.4.0" }
        hasBin: true
        dev: false

    /agent-base@6.0.2:
        resolution:
            {
                integrity: sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=,
                tarball: http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz,
            }
        engines: { node: ">= 6.0.0" }
        dependencies:
            debug: 4.4.1
        transitivePeerDependencies:
            - supports-color
        dev: false

    /asynckit@0.4.0:
        resolution:
            {
                integrity: sha1-x57Zf380y48robyXkLzDZkdLS3k=,
                tarball: http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz,
            }
        dev: false

    /axios@0.21.1:
        resolution:
            {
                integrity: sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=,
                tarball: http://r.npm.sankuai.com/axios/download/axios-0.21.1.tgz,
            }
        dependencies:
            follow-redirects: 1.15.9
        transitivePeerDependencies:
            - debug
        dev: false

    /base64-arraybuffer@0.2.0:
        resolution:
            {
                integrity: sha1-S5RPrAGRqlkHr+LYyZnMxXzoD0U=,
                tarball: http://r.npm.sankuai.com/base64-arraybuffer/download/base64-arraybuffer-0.2.0.tgz,
            }
        engines: { node: ">= 0.6.0" }
        dev: false

    /base64-arraybuffer@1.0.2:
        resolution:
            {
                integrity: sha1-HDdYmnxLB0bjS9H+uVHaLfAcG9w=,
                tarball: http://r.npm.sankuai.com/base64-arraybuffer/download/base64-arraybuffer-1.0.2.tgz,
            }
        engines: { node: ">= 0.6.0" }
        dev: false

    /browser-process-hrtime@1.0.0:
        resolution:
            {
                integrity: sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=,
                tarball: http://r.npm.sankuai.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz,
            }
        dev: false

    /call-bind-apply-helpers@1.0.2:
        resolution:
            {
                integrity: sha1-S1QowiK+mF15w9gmV0edvgtZstY=,
                tarball: http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            es-errors: 1.3.0
            function-bind: 1.1.2
        dev: false

    /combined-stream@1.0.8:
        resolution:
            {
                integrity: sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=,
                tarball: http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz,
            }
        engines: { node: ">= 0.8" }
        dependencies:
            delayed-stream: 1.0.0
        dev: false

    /css-line-break@2.0.1:
        resolution:
            {
                integrity: sha1-PcdMLtXrZCEUgCgZMkdXkCQ+czg=,
                tarball: http://r.npm.sankuai.com/css-line-break/download/css-line-break-2.0.1.tgz,
            }
        dependencies:
            base64-arraybuffer: 0.2.0
        dev: false

    /css-line-break@2.1.0:
        resolution:
            {
                integrity: sha1-v+9mDfpvU5fqVBFrs8tIc+28T6A=,
                tarball: http://r.npm.sankuai.com/css-line-break/download/css-line-break-2.1.0.tgz,
            }
        dependencies:
            utrie: 1.0.2
        dev: false

    /cssom@0.3.8:
        resolution:
            {
                integrity: sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=,
                tarball: http://r.npm.sankuai.com/cssom/download/cssom-0.3.8.tgz,
            }
        dev: false

    /cssom@0.4.4:
        resolution:
            {
                integrity: sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=,
                tarball: http://r.npm.sankuai.com/cssom/download/cssom-0.4.4.tgz,
            }
        dev: false

    /cssstyle@2.3.0:
        resolution:
            {
                integrity: sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=,
                tarball: http://r.npm.sankuai.com/cssstyle/download/cssstyle-2.3.0.tgz,
            }
        engines: { node: ">=8" }
        dependencies:
            cssom: 0.3.8
        dev: false

    /data-urls@2.0.0:
        resolution:
            {
                integrity: sha1-FWSFpyljqXD11YIar2Qr7yvy25s=,
                tarball: http://r.npm.sankuai.com/data-urls/download/data-urls-2.0.0.tgz,
            }
        engines: { node: ">=10" }
        dependencies:
            abab: 2.0.6
            whatwg-mimetype: 2.3.0
            whatwg-url: 8.7.0
        dev: false

    /debug@4.4.1:
        resolution:
            {
                integrity: sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=,
                tarball: http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz,
            }
        engines: { node: ">=6.0" }
        peerDependencies:
            supports-color: "*"
        peerDependenciesMeta:
            supports-color:
                optional: true
        dependencies:
            ms: 2.1.3
        dev: false

    /decimal.js@10.5.0:
        resolution:
            {
                integrity: sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=,
                tarball: http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.5.0.tgz,
            }
        dev: false

    /delayed-stream@1.0.0:
        resolution:
            {
                integrity: sha1-3zrhmayt+31ECqrgsp4icrJOxhk=,
                tarball: http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz,
            }
        engines: { node: ">=0.4.0" }
        dev: false

    /domexception@2.0.1:
        resolution:
            {
                integrity: sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=,
                tarball: http://r.npm.sankuai.com/domexception/download/domexception-2.0.1.tgz,
            }
        engines: { node: ">=8" }
        deprecated: Use your platform's native DOMException instead
        dependencies:
            webidl-conversions: 5.0.0
        dev: false

    /dunder-proto@1.0.1:
        resolution:
            {
                integrity: sha1-165mfh3INIL4tw/Q9u78UNow9Yo=,
                tarball: http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            call-bind-apply-helpers: 1.0.2
            es-errors: 1.3.0
            gopd: 1.2.0
        dev: false

    /es-define-property@1.0.1:
        resolution:
            {
                integrity: sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=,
                tarball: http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: false

    /es-errors@1.3.0:
        resolution:
            {
                integrity: sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=,
                tarball: http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: false

    /es-object-atoms@1.1.1:
        resolution:
            {
                integrity: sha1-HE8sSDcydZfOadLKGQp/3RcjOME=,
                tarball: http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            es-errors: 1.3.0
        dev: false

    /es-set-tostringtag@2.1.0:
        resolution:
            {
                integrity: sha1-8x274MGDsAptJutjJcgQwP0YvU0=,
                tarball: http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            es-errors: 1.3.0
            get-intrinsic: 1.3.0
            has-tostringtag: 1.0.2
            hasown: 2.0.2
        dev: false

    /esbuild-android-64@0.14.54:
        resolution:
            {
                integrity: sha1-UF9BgyiEMTu6/7J3BLi8qi2GFr4=,
                tarball: http://r.npm.sankuai.com/esbuild-android-64/download/esbuild-android-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [android]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-android-arm64@0.14.54:
        resolution:
            {
                integrity: sha1-jOadfKuklkbgCZaP5XVKIamHF3E=,
                tarball: http://r.npm.sankuai.com/esbuild-android-arm64/download/esbuild-android-arm64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [android]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-darwin-64@0.14.54:
        resolution:
            {
                integrity: sha1-JLpnuajLiQo8CNkBj4h8wiHN2iU=,
                tarball: http://r.npm.sankuai.com/esbuild-darwin-64/download/esbuild-darwin-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [darwin]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-darwin-arm64@0.14.54:
        resolution:
            {
                integrity: sha1-P3zbeIiO4F5IjSUKK9qrH6Zxv3M=,
                tarball: http://r.npm.sankuai.com/esbuild-darwin-arm64/download/esbuild-darwin-arm64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [darwin]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-freebsd-64@0.14.54:
        resolution:
            {
                integrity: sha1-CSUPmXpW7UZQ8+GXnJBf/EC76U0=,
                tarball: http://r.npm.sankuai.com/esbuild-freebsd-64/download/esbuild-freebsd-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [freebsd]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-freebsd-arm64@0.14.54:
        resolution:
            {
                integrity: sha1-uvtG7QT8X5fL2wFthpR6eVefjkg=,
                tarball: http://r.npm.sankuai.com/esbuild-freebsd-arm64/download/esbuild-freebsd-arm64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [freebsd]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-32@0.14.54:
        resolution:
            {
                integrity: sha1-4qjEqO/cNVQFMlAz/OvrlB94H+U=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-32/download/esbuild-linux-32-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-64@0.14.54:
        resolution:
            {
                integrity: sha1-3l/boclWZs9yNp9StAsDvnEiZlI=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-64/download/esbuild-linux-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-arm64@0.14.54:
        resolution:
            {
                integrity: sha1-2uTNQq6Xh0aLalwVjaTIToOwzos=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-arm64/download/esbuild-linux-arm64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-arm@0.14.54:
        resolution:
            {
                integrity: sha1-osHf9tDyHb6PxpmKEiZ1Uz3fzVk=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-arm/download/esbuild-linux-arm-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-mips64le@0.14.54:
        resolution:
            {
                integrity: sha1-2ZGOnky5cvjW2ujoZVv57hMe2jQ=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-mips64le/download/esbuild-linux-mips64le-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [mips64el]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-ppc64le@0.14.54:
        resolution:
            {
                integrity: sha1-P5oPbUEHP7GmQGgIRcfeUplfE34=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-ppc64le/download/esbuild-linux-ppc64le-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [ppc64]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-riscv64@0.14.54:
        resolution:
            {
                integrity: sha1-YYhTwCgXimGDe8eZ0gE9RpXkUcg=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-riscv64/download/esbuild-linux-riscv64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [riscv64]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-linux-s390x@0.14.54:
        resolution:
            {
                integrity: sha1-0YhcTFp2u7Wg/hguLIxg654p8qY=,
                tarball: http://r.npm.sankuai.com/esbuild-linux-s390x/download/esbuild-linux-s390x-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [s390x]
        os: [linux]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-netbsd-64@0.14.54:
        resolution:
            {
                integrity: sha1-aa6Rei/yQbffHb8iuvBL0zA0noE=,
                tarball: http://r.npm.sankuai.com/esbuild-netbsd-64/download/esbuild-netbsd-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [netbsd]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-openbsd-64@0.14.54:
        resolution:
            {
                integrity: sha1-20yElSh6NQpnkN4i7eokelfF1Hs=,
                tarball: http://r.npm.sankuai.com/esbuild-openbsd-64/download/esbuild-openbsd-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [openbsd]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-sunos-64@0.14.54:
        resolution:
            {
                integrity: sha1-VCh+49pz04RLchwhvIDB3H4b99o=,
                tarball: http://r.npm.sankuai.com/esbuild-sunos-64/download/esbuild-sunos-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [sunos]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-windows-32@0.14.54:
        resolution:
            {
                integrity: sha1-+Kr5pWZ2MLQPD7OqN78Bu9NAzjE=,
                tarball: http://r.npm.sankuai.com/esbuild-windows-32/download/esbuild-windows-32-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [ia32]
        os: [win32]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-windows-64@0.14.54:
        resolution:
            {
                integrity: sha1-v1S1G9PpsPGIb/2yJKQXYDHqCvQ=,
                tarball: http://r.npm.sankuai.com/esbuild-windows-64/download/esbuild-windows-64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [x64]
        os: [win32]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild-windows-arm64@0.14.54:
        resolution:
            {
                integrity: sha1-k30VZ1oV5LDk+v26o6Aad2or6YI=,
                tarball: http://r.npm.sankuai.com/esbuild-windows-arm64/download/esbuild-windows-arm64-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        cpu: [arm64]
        os: [win32]
        requiresBuild: true
        dev: true
        optional: true

    /esbuild@0.14.54:
        resolution:
            {
                integrity: sha1-i0Tc8rDxpm/CJFmUPcz0d1NemqI=,
                tarball: http://r.npm.sankuai.com/esbuild/download/esbuild-0.14.54.tgz,
            }
        engines: { node: ">=12" }
        hasBin: true
        requiresBuild: true
        optionalDependencies:
            "@esbuild/linux-loong64": 0.14.54
            esbuild-android-64: 0.14.54
            esbuild-android-arm64: 0.14.54
            esbuild-darwin-64: 0.14.54
            esbuild-darwin-arm64: 0.14.54
            esbuild-freebsd-64: 0.14.54
            esbuild-freebsd-arm64: 0.14.54
            esbuild-linux-32: 0.14.54
            esbuild-linux-64: 0.14.54
            esbuild-linux-arm: 0.14.54
            esbuild-linux-arm64: 0.14.54
            esbuild-linux-mips64le: 0.14.54
            esbuild-linux-ppc64le: 0.14.54
            esbuild-linux-riscv64: 0.14.54
            esbuild-linux-s390x: 0.14.54
            esbuild-netbsd-64: 0.14.54
            esbuild-openbsd-64: 0.14.54
            esbuild-sunos-64: 0.14.54
            esbuild-windows-32: 0.14.54
            esbuild-windows-64: 0.14.54
            esbuild-windows-arm64: 0.14.54
        dev: true

    /esbuild@0.8.57:
        resolution:
            {
                integrity: sha1-pC0CvCtXxwvNDviX/iRHZrtt2SY=,
                tarball: http://r.npm.sankuai.com/esbuild/download/esbuild-0.8.57.tgz,
            }
        hasBin: true
        requiresBuild: true
        dev: false

    /escodegen@2.1.0:
        resolution:
            {
                integrity: sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=,
                tarball: http://r.npm.sankuai.com/escodegen/download/escodegen-2.1.0.tgz,
            }
        engines: { node: ">=6.0" }
        hasBin: true
        dependencies:
            esprima: 4.0.1
            estraverse: 5.3.0
            esutils: 2.0.3
        optionalDependencies:
            source-map: 0.6.1
        dev: false

    /esprima@4.0.1:
        resolution:
            {
                integrity: sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=,
                tarball: http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz,
            }
        engines: { node: ">=4" }
        hasBin: true
        dev: false

    /estraverse@5.3.0:
        resolution:
            {
                integrity: sha1-LupSkHAvJquP5TcDcP+GyWXSESM=,
                tarball: http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz,
            }
        engines: { node: ">=4.0" }
        dev: false

    /estree-walker@1.0.1:
        resolution:
            {
                integrity: sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=,
                tarball: http://r.npm.sankuai.com/estree-walker/download/estree-walker-1.0.1.tgz,
            }
        dev: false

    /esutils@2.0.3:
        resolution:
            {
                integrity: sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=,
                tarball: http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz,
            }
        engines: { node: ">=0.10.0" }
        dev: false

    /follow-redirects@1.15.9:
        resolution:
            {
                integrity: sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=,
                tarball: http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz,
            }
        engines: { node: ">=4.0" }
        peerDependencies:
            debug: "*"
        peerDependenciesMeta:
            debug:
                optional: true
        dev: false

    /form-data@3.0.3:
        resolution:
            {
                integrity: sha1-NJyPLJ2PjwyHnuDrfMDTAAGNawk=,
                tarball: http://r.npm.sankuai.com/form-data/download/form-data-3.0.3.tgz,
            }
        engines: { node: ">= 6" }
        dependencies:
            asynckit: 0.4.0
            combined-stream: 1.0.8
            es-set-tostringtag: 2.1.0
            mime-types: 2.1.35
        dev: false

    /fsevents@2.3.3:
        resolution:
            {
                integrity: sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=,
                tarball: http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz,
            }
        engines: { node: ^8.16.0 || ^10.6.0 || >=11.0.0 }
        os: [darwin]
        requiresBuild: true
        optional: true

    /function-bind@1.1.2:
        resolution:
            {
                integrity: sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=,
                tarball: http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz,
            }

    /get-intrinsic@1.3.0:
        resolution:
            {
                integrity: sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=,
                tarball: http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            call-bind-apply-helpers: 1.0.2
            es-define-property: 1.0.1
            es-errors: 1.3.0
            es-object-atoms: 1.1.1
            function-bind: 1.1.2
            get-proto: 1.0.1
            gopd: 1.2.0
            has-symbols: 1.1.0
            hasown: 2.0.2
            math-intrinsics: 1.1.0
        dev: false

    /get-proto@1.0.1:
        resolution:
            {
                integrity: sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=,
                tarball: http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            dunder-proto: 1.0.1
            es-object-atoms: 1.1.1
        dev: false

    /gopd@1.2.0:
        resolution:
            {
                integrity: sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=,
                tarball: http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: false

    /has-symbols@1.1.0:
        resolution:
            {
                integrity: sha1-/JxqeDoISVHQuXH+EBjegTcHozg=,
                tarball: http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: false

    /has-tostringtag@1.0.2:
        resolution:
            {
                integrity: sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=,
                tarball: http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            has-symbols: 1.1.0
        dev: false

    /hasown@2.0.2:
        resolution:
            {
                integrity: sha1-AD6vkb563DcuhOxZ3DclLO24AAM=,
                tarball: http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            function-bind: 1.1.2

    /html-encoding-sniffer@2.0.1:
        resolution:
            {
                integrity: sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=,
                tarball: http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz,
            }
        engines: { node: ">=10" }
        dependencies:
            whatwg-encoding: 1.0.5
        dev: false

    /html2canvas@1.4.1:
        resolution:
            {
                integrity: sha1-fO8YiDEbUBHVB3lKBmBBsUZppUM=,
                tarball: http://r.npm.sankuai.com/html2canvas/download/html2canvas-1.4.1.tgz,
            }
        engines: { node: ">=8.0.0" }
        dependencies:
            css-line-break: 2.1.0
            text-segmentation: 1.0.3
        dev: false

    /http-proxy-agent@4.0.1:
        resolution:
            {
                integrity: sha1-ioyO9/WTLM+VPClsqCkblap0qjo=,
                tarball: http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz,
            }
        engines: { node: ">= 6" }
        dependencies:
            "@tootallnate/once": 1.1.2
            agent-base: 6.0.2
            debug: 4.4.1
        transitivePeerDependencies:
            - supports-color
        dev: false

    /https-proxy-agent@5.0.1:
        resolution:
            {
                integrity: sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=,
                tarball: http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz,
            }
        engines: { node: ">= 6" }
        dependencies:
            agent-base: 6.0.2
            debug: 4.4.1
        transitivePeerDependencies:
            - supports-color
        dev: false

    /iconv-lite@0.4.24:
        resolution:
            {
                integrity: sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=,
                tarball: http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz,
            }
        engines: { node: ">=0.10.0" }
        dependencies:
            safer-buffer: 2.1.2
        dev: false

    /is-core-module@2.16.1:
        resolution:
            {
                integrity: sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=,
                tarball: http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz,
            }
        engines: { node: ">= 0.4" }
        dependencies:
            hasown: 2.0.2
        dev: true

    /is-potential-custom-element-name@1.0.1:
        resolution:
            {
                integrity: sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=,
                tarball: http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz,
            }
        dev: false

    /jsdom@16.7.0:
        resolution:
            {
                integrity: sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=,
                tarball: http://r.npm.sankuai.com/jsdom/download/jsdom-16.7.0.tgz,
            }
        engines: { node: ">=10" }
        peerDependencies:
            canvas: ^2.5.0
        peerDependenciesMeta:
            canvas:
                optional: true
        dependencies:
            abab: 2.0.6
            acorn: 8.14.1
            acorn-globals: 6.0.0
            cssom: 0.4.4
            cssstyle: 2.3.0
            data-urls: 2.0.0
            decimal.js: 10.5.0
            domexception: 2.0.1
            escodegen: 2.1.0
            form-data: 3.0.3
            html-encoding-sniffer: 2.0.1
            http-proxy-agent: 4.0.1
            https-proxy-agent: 5.0.1
            is-potential-custom-element-name: 1.0.1
            nwsapi: 2.2.20
            parse5: 6.0.1
            saxes: 5.0.1
            symbol-tree: 3.2.4
            tough-cookie: 4.1.4
            w3c-hr-time: 1.0.2
            w3c-xmlserializer: 2.0.0
            webidl-conversions: 6.1.0
            whatwg-encoding: 1.0.5
            whatwg-mimetype: 2.3.0
            whatwg-url: 8.7.0
            ws: 7.5.10
            xml-name-validator: 3.0.0
        transitivePeerDependencies:
            - bufferutil
            - supports-color
            - utf-8-validate
        dev: false

    /lodash@4.17.21:
        resolution:
            {
                integrity: sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=,
                tarball: http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz,
            }
        dev: false

    /math-intrinsics@1.1.0:
        resolution:
            {
                integrity: sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=,
                tarball: http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: false

    /mime-db@1.52.0:
        resolution:
            {
                integrity: sha1-u6vNwChZ9JhzAchW4zh85exDv3A=,
                tarball: http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz,
            }
        engines: { node: ">= 0.6" }
        dev: false

    /mime-types@2.1.35:
        resolution:
            {
                integrity: sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=,
                tarball: http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz,
            }
        engines: { node: ">= 0.6" }
        dependencies:
            mime-db: 1.52.0
        dev: false

    /ms@2.1.3:
        resolution:
            {
                integrity: sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=,
                tarball: http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz,
            }
        dev: false

    /nanoid@3.3.11:
        resolution:
            {
                integrity: sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=,
                tarball: http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz,
            }
        engines: { node: ^10 || ^12 || ^13.7 || ^14 || >=15.0.1 }
        hasBin: true
        dev: true

    /nwsapi@2.2.20:
        resolution:
            {
                integrity: sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=,
                tarball: http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.20.tgz,
            }
        dev: false

    /parse5@6.0.1:
        resolution:
            {
                integrity: sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=,
                tarball: http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz,
            }
        dev: false

    /path-parse@1.0.7:
        resolution:
            {
                integrity: sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=,
                tarball: http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz,
            }
        dev: true

    /picocolors@1.1.1:
        resolution:
            {
                integrity: sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=,
                tarball: http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz,
            }
        dev: true

    /picomatch@2.3.1:
        resolution:
            {
                integrity: sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=,
                tarball: http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz,
            }
        engines: { node: ">=8.6" }
        dev: false

    /postcss@8.5.3:
        resolution:
            {
                integrity: sha1-FGO28cf7Fv4lhzbLopot41I36vs=,
                tarball: http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz,
            }
        engines: { node: ^10 || ^12 || >=14 }
        dependencies:
            nanoid: 3.3.11
            picocolors: 1.1.1
            source-map-js: 1.2.1
        dev: true

    /psl@1.15.0:
        resolution:
            {
                integrity: sha1-vazjGJbx2XzsannoIkiYzpPZdMY=,
                tarball: http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz,
            }
        dependencies:
            punycode: 2.3.1
        dev: false

    /punycode@2.3.1:
        resolution:
            {
                integrity: sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=,
                tarball: http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz,
            }
        engines: { node: ">=6" }
        dev: false

    /querystringify@2.2.0:
        resolution:
            {
                integrity: sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=,
                tarball: http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz,
            }
        dev: false

    /requires-port@1.0.0:
        resolution:
            {
                integrity: sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=,
                tarball: http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz,
            }
        dev: false

    /resolve@1.22.10:
        resolution:
            {
                integrity: sha1-tmPoP/sJu/I4aURza6roAwKbizk=,
                tarball: http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz,
            }
        engines: { node: ">= 0.4" }
        hasBin: true
        dependencies:
            is-core-module: 2.16.1
            path-parse: 1.0.7
            supports-preserve-symlinks-flag: 1.0.0
        dev: true

    /rollup@2.77.3:
        resolution:
            {
                integrity: sha1-jwBBjTonQANuFd62U77RqQ7gzBI=,
                tarball: http://r.npm.sankuai.com/rollup/download/rollup-2.77.3.tgz,
            }
        engines: { node: ">=10.0.0" }
        hasBin: true
        optionalDependencies:
            fsevents: 2.3.3
        dev: true

    /rollup@2.79.2:
        resolution:
            {
                integrity: sha1-8VDkpdtLEhohp0fXYvcB5en0kJA=,
                tarball: http://r.npm.sankuai.com/rollup/download/rollup-2.79.2.tgz,
            }
        engines: { node: ">=10.0.0" }
        hasBin: true
        optionalDependencies:
            fsevents: 2.3.3
        dev: false

    /safer-buffer@2.1.2:
        resolution:
            {
                integrity: sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=,
                tarball: http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz,
            }
        dev: false

    /saxes@5.0.1:
        resolution:
            {
                integrity: sha1-7rq5U/o7dgjb6U5drbFciI+maW0=,
                tarball: http://r.npm.sankuai.com/saxes/download/saxes-5.0.1.tgz,
            }
        engines: { node: ">=10" }
        dependencies:
            xmlchars: 2.2.0
        dev: false

    /source-map-js@1.2.1:
        resolution:
            {
                integrity: sha1-HOVlD93YerwJnto33P8CTCZnrkY=,
                tarball: http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz,
            }
        engines: { node: ">=0.10.0" }
        dev: true

    /source-map@0.6.1:
        resolution:
            {
                integrity: sha1-dHIq8y6WFOnCh6jQu95IteLxomM=,
                tarball: http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz,
            }
        engines: { node: ">=0.10.0" }
        requiresBuild: true
        dev: false
        optional: true

    /supports-preserve-symlinks-flag@1.0.0:
        resolution:
            {
                integrity: sha1-btpL00SjyUrqN21MwxvHcxEDngk=,
                tarball: http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz,
            }
        engines: { node: ">= 0.4" }
        dev: true

    /symbol-tree@3.2.4:
        resolution:
            {
                integrity: sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=,
                tarball: http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz,
            }
        dev: false

    /text-segmentation@1.0.3:
        resolution:
            {
                integrity: sha1-UqOIFZ7//nRrJKY7oxG2rJ8teUM=,
                tarball: http://r.npm.sankuai.com/text-segmentation/download/text-segmentation-1.0.3.tgz,
            }
        dependencies:
            utrie: 1.0.2
        dev: false

    /tough-cookie@4.1.4:
        resolution:
            {
                integrity: sha1-lF8UYbRbWox2ghwz6knDrBksGzY=,
                tarball: http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.4.tgz,
            }
        engines: { node: ">=6" }
        dependencies:
            psl: 1.15.0
            punycode: 2.3.1
            universalify: 0.2.0
            url-parse: 1.5.10
        dev: false

    /tr46@2.1.0:
        resolution:
            {
                integrity: sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=,
                tarball: http://r.npm.sankuai.com/tr46/download/tr46-2.1.0.tgz,
            }
        engines: { node: ">=8" }
        dependencies:
            punycode: 2.3.1
        dev: false

    /typescript@4.9.5:
        resolution:
            {
                integrity: sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=,
                tarball: http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz,
            }
        engines: { node: ">=4.2.0" }
        hasBin: true
        dev: true

    /universalify@0.2.0:
        resolution:
            {
                integrity: sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=,
                tarball: http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz,
            }
        engines: { node: ">= 4.0.0" }
        dev: false

    /url-parse@1.5.10:
        resolution:
            {
                integrity: sha1-nTwvc2wddd070r5QfcwRHx4uqcE=,
                tarball: http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz,
            }
        dependencies:
            querystringify: 2.2.0
            requires-port: 1.0.0
        dev: false

    /utrie@1.0.2:
        resolution:
            {
                integrity: sha1-1C/kTem8ARnCXef1ZKbtGyyHpkU=,
                tarball: http://r.npm.sankuai.com/utrie/download/utrie-1.0.2.tgz,
            }
        dependencies:
            base64-arraybuffer: 1.0.2
        dev: false

    /vite@2.9.18:
        resolution:
            {
                integrity: sha1-dOKoOynageYC2sTCkzEsxXXwkcc=,
                tarball: http://r.npm.sankuai.com/vite/download/vite-2.9.18.tgz,
            }
        engines: { node: ">=12.2.0" }
        hasBin: true
        peerDependencies:
            less: "*"
            sass: "*"
            stylus: "*"
        peerDependenciesMeta:
            less:
                optional: true
            sass:
                optional: true
            stylus:
                optional: true
        dependencies:
            esbuild: 0.14.54
            postcss: 8.5.3
            resolve: 1.22.10
            rollup: 2.77.3
        optionalDependencies:
            fsevents: 2.3.3
        dev: true

    /w3c-hr-time@1.0.2:
        resolution:
            {
                integrity: sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=,
                tarball: http://r.npm.sankuai.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz,
            }
        dependencies:
            browser-process-hrtime: 1.0.0
        dev: false

    /w3c-xmlserializer@2.0.0:
        resolution:
            {
                integrity: sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=,
                tarball: http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz,
            }
        engines: { node: ">=10" }
        dependencies:
            xml-name-validator: 3.0.0
        dev: false

    /webidl-conversions@5.0.0:
        resolution:
            {
                integrity: sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=,
                tarball: http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz,
            }
        engines: { node: ">=8" }
        dev: false

    /webidl-conversions@6.1.0:
        resolution:
            {
                integrity: sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=,
                tarball: http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz,
            }
        engines: { node: ">=10.4" }
        dev: false

    /whatwg-encoding@1.0.5:
        resolution:
            {
                integrity: sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=,
                tarball: http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz,
            }
        dependencies:
            iconv-lite: 0.4.24
        dev: false

    /whatwg-mimetype@2.3.0:
        resolution:
            {
                integrity: sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=,
                tarball: http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz,
            }
        dev: false

    /whatwg-url@8.7.0:
        resolution:
            {
                integrity: sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=,
                tarball: http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-8.7.0.tgz,
            }
        engines: { node: ">=10" }
        dependencies:
            lodash: 4.17.21
            tr46: 2.1.0
            webidl-conversions: 6.1.0
        dev: false

    /ws@7.5.10:
        resolution:
            {
                integrity: sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=,
                tarball: http://r.npm.sankuai.com/ws/download/ws-7.5.10.tgz,
            }
        engines: { node: ">=8.3.0" }
        peerDependencies:
            bufferutil: ^4.0.1
            utf-8-validate: ^5.0.2
        peerDependenciesMeta:
            bufferutil:
                optional: true
            utf-8-validate:
                optional: true
        dev: false

    /xml-name-validator@3.0.0:
        resolution:
            {
                integrity: sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=,
                tarball: http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz,
            }
        dev: false

    /xmlchars@2.2.0:
        resolution:
            {
                integrity: sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=,
                tarball: http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz,
            }
        dev: false
