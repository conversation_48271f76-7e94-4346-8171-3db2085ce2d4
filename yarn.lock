# THIS IS AN AUTOGENERATED FILE. DO NOT EDIT THIS FILE DIRECTLY.
# yarn lockfile v1


"@ampproject/worker-dom@0.30.1":
  version "0.30.1"
  resolved "http://r.npm.sankuai.com/@ampproject/worker-dom/download/@ampproject/worker-dom-0.30.1.tgz#aa58e59770d8e2344876506cb88dd0516a6e3fc2"
  integrity sha1-qljll3DY4jRIdlBsuI3QUWpuP8I=

"@nibfe/white-screen-sdk@0.2.4":
  version "0.2.4"
  resolved "http://r.npm.sankuai.com/@nibfe/white-screen-sdk/download/@nibfe/white-screen-sdk-0.2.4.tgz#c489a42dceb7653c436c582d1a968e65843647eb"
  integrity sha1-xImkLc63ZTxDbFgtGpaOZYQ2R+s=
  dependencies:
    "@ampproject/worker-dom" "0.30.1"
    "@rollup/plugin-json" "4.1.0"
    "@throne/html2canvas" "0.0.2-beta.0"
    axios "0.21.1"
    jsdom "16.7.0"

"@rollup/plugin-json@4.1.0":
  version "4.1.0"
  resolved "http://r.npm.sankuai.com/@rollup/plugin-json/download/@rollup/plugin-json-4.1.0.tgz#54e09867ae6963c593844d8bd7a9c718294496f3"
  integrity sha1-VOCYZ65pY8WThE2L16nHGClElvM=
  dependencies:
    "@rollup/pluginutils" "^3.0.8"

"@rollup/pluginutils@^3.0.8":
  version "3.1.0"
  resolved "http://r.npm.sankuai.com/@rollup/pluginutils/download/@rollup/pluginutils-3.1.0.tgz#706b4524ee6dc8b103b3c995533e5ad680c02b9b"
  integrity sha1-cGtFJO5tyLEDs8mVUz5a1oDAK5s=
  dependencies:
    "@types/estree" "0.0.39"
    estree-walker "^1.0.1"
    picomatch "^2.2.2"

"@throne/html2canvas@0.0.2-beta.0":
  version "0.0.2-beta.0"
  resolved "http://r.npm.sankuai.com/@throne/html2canvas/download/@throne/html2canvas-0.0.2-beta.0.tgz#a4788c17530f6d105e4160dfc52cd5583d0924be"
  integrity sha1-pHiMF1MPbRBeQWDfxSzVWD0JJL4=
  dependencies:
    css-line-break "2.0.1"
    text-segmentation "^1.0.2"

"@tootallnate/once@1":
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/@tootallnate/once/download/@tootallnate/once-1.1.2.tgz#ccb91445360179a04e7fe6aff78c00ffc1eeaf82"
  integrity sha1-zLkURTYBeaBOf+av94wA/8Hur4I=

"@types/estree@0.0.39":
  version "0.0.39"
  resolved "http://r.npm.sankuai.com/@types/estree/download/@types/estree-0.0.39.tgz#e177e699ee1b8c22d23174caaa7422644389509f"
  integrity sha1-4Xfmme4bjCLSMXTKqnQiZEOJUJ8=

abab@^2.0.3, abab@^2.0.5:
  version "2.0.6"
  resolved "http://r.npm.sankuai.com/abab/download/abab-2.0.6.tgz#41b80f2c871d19686216b82309231cfd3cb3d291"
  integrity sha1-QbgPLIcdGWhiFrgjCSMc/Tyz0pE=

acorn-globals@^6.0.0:
  version "6.0.0"
  resolved "http://r.npm.sankuai.com/acorn-globals/download/acorn-globals-6.0.0.tgz#46cdd39f0f8ff08a876619b55f5ac8a6dc770b45"
  integrity sha1-Rs3Tnw+P8IqHZhm1X1rIptx3C0U=
  dependencies:
    acorn "^7.1.1"
    acorn-walk "^7.1.1"

acorn-walk@^7.1.1:
  version "7.2.0"
  resolved "http://r.npm.sankuai.com/acorn-walk/download/acorn-walk-7.2.0.tgz#0de889a601203909b0fbe07b8938dc21d2e967bc"
  integrity sha1-DeiJpgEgOQmw++B7iTjcIdLpZ7w=

acorn@^7.1.1:
  version "7.4.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-7.4.1.tgz#feaed255973d2e77555b83dbc08851a6c63520fa"
  integrity sha1-/q7SVZc9LndVW4PbwIhRpsY1IPo=

acorn@^8.2.4:
  version "8.14.1"
  resolved "http://r.npm.sankuai.com/acorn/download/acorn-8.14.1.tgz#721d5dc10f7d5b5609a891773d47731796935dfb"
  integrity sha1-ch1dwQ99W1YJqJF3PUdzF5aTXfs=

agent-base@6:
  version "6.0.2"
  resolved "http://r.npm.sankuai.com/agent-base/download/agent-base-6.0.2.tgz#49fff58577cfee3f37176feab4c22e00f86d7f77"
  integrity sha1-Sf/1hXfP7j83F2/qtMIuAPhtf3c=
  dependencies:
    debug "4"

asynckit@^0.4.0:
  version "0.4.0"
  resolved "http://r.npm.sankuai.com/asynckit/download/asynckit-0.4.0.tgz#c79ed97f7f34cb8f2ba1bc9790bcc366474b4b79"
  integrity sha1-x57Zf380y48robyXkLzDZkdLS3k=

axios@0.21.1:
  version "0.21.1"
  resolved "http://r.npm.sankuai.com/axios/download/axios-0.21.1.tgz#22563481962f4d6bde9a76d516ef0e5d3c09b2b8"
  integrity sha1-IlY0gZYvTWvemnbVFu8OXTwJsrg=
  dependencies:
    follow-redirects "^1.10.0"

base64-arraybuffer@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/base64-arraybuffer/download/base64-arraybuffer-0.2.0.tgz#4b944fac0191aa5907afe2d8c999ccc57ce80f45"
  integrity sha1-S5RPrAGRqlkHr+LYyZnMxXzoD0U=

base64-arraybuffer@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/base64-arraybuffer/download/base64-arraybuffer-1.0.2.tgz#1c37589a7c4b0746e34bd1feb951da2df01c1bdc"
  integrity sha1-HDdYmnxLB0bjS9H+uVHaLfAcG9w=

browser-process-hrtime@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/browser-process-hrtime/download/browser-process-hrtime-1.0.0.tgz#3c9b4b7d782c8121e56f10106d84c0d0ffc94626"
  integrity sha1-PJtLfXgsgSHlbxAQbYTA0P/JRiY=

call-bind-apply-helpers@^1.0.1, call-bind-apply-helpers@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/call-bind-apply-helpers/download/call-bind-apply-helpers-1.0.2.tgz#4b5428c222be985d79c3d82657479dbe0b59b2d6"
  integrity sha1-S1QowiK+mF15w9gmV0edvgtZstY=
  dependencies:
    es-errors "^1.3.0"
    function-bind "^1.1.2"

combined-stream@^1.0.8:
  version "1.0.8"
  resolved "http://r.npm.sankuai.com/combined-stream/download/combined-stream-1.0.8.tgz#c3d45a8b34fd730631a110a8a2520682b31d5a7f"
  integrity sha1-w9RaizT9cwYxoRCoolIGgrMdWn8=
  dependencies:
    delayed-stream "~1.0.0"

css-line-break@2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/css-line-break/download/css-line-break-2.0.1.tgz#3dc74c2ed5eb64211480281932475790243e7338"
  integrity sha1-PcdMLtXrZCEUgCgZMkdXkCQ+czg=
  dependencies:
    base64-arraybuffer "^0.2.0"

css-line-break@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/css-line-break/download/css-line-break-2.1.0.tgz#bfef660dfa6f5397ea54116bb3cb4873edbc4fa0"
  integrity sha1-v+9mDfpvU5fqVBFrs8tIc+28T6A=
  dependencies:
    utrie "^1.0.2"

cssom@^0.4.4:
  version "0.4.4"
  resolved "http://r.npm.sankuai.com/cssom/download/cssom-0.4.4.tgz#5a66cf93d2d0b661d80bf6a44fb65f5c2e4e0a10"
  integrity sha1-WmbPk9LQtmHYC/akT7ZfXC5OChA=

cssom@~0.3.6:
  version "0.3.8"
  resolved "http://r.npm.sankuai.com/cssom/download/cssom-0.3.8.tgz#9f1276f5b2b463f2114d3f2c75250af8c1a36f4a"
  integrity sha1-nxJ29bK0Y/IRTT8sdSUK+MGjb0o=

cssstyle@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/cssstyle/download/cssstyle-2.3.0.tgz#ff665a0ddbdc31864b09647f34163443d90b0852"
  integrity sha1-/2ZaDdvcMYZLCWR/NBY0Q9kLCFI=
  dependencies:
    cssom "~0.3.6"

data-urls@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/data-urls/download/data-urls-2.0.0.tgz#156485a72963a970f5d5821aaf642bef2bf2db9b"
  integrity sha1-FWSFpyljqXD11YIar2Qr7yvy25s=
  dependencies:
    abab "^2.0.3"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.0.0"

debug@4:
  version "4.4.1"
  resolved "http://r.npm.sankuai.com/debug/download/debug-4.4.1.tgz#e5a8bc6cbc4c6cd3e64308b0693a3d4fa550189b"
  integrity sha1-5ai8bLxMbNPmQwiwaTo9T6VQGJs=
  dependencies:
    ms "^2.1.3"

decimal.js@^10.2.1:
  version "10.5.0"
  resolved "http://r.npm.sankuai.com/decimal.js/download/decimal.js-10.5.0.tgz#0f371c7cf6c4898ce0afb09836db73cd82010f22"
  integrity sha1-DzccfPbEiYzgr7CYNttzzYIBDyI=

delayed-stream@~1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/delayed-stream/download/delayed-stream-1.0.0.tgz#df3ae199acadfb7d440aaae0b29e2272b24ec619"
  integrity sha1-3zrhmayt+31ECqrgsp4icrJOxhk=

domexception@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/domexception/download/domexception-2.0.1.tgz#fb44aefba793e1574b0af6aed2801d057529f304"
  integrity sha1-+0Su+6eT4VdLCvau0oAdBXUp8wQ=
  dependencies:
    webidl-conversions "^5.0.0"

dunder-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/dunder-proto/download/dunder-proto-1.0.1.tgz#d7ae667e1dc83482f8b70fd0f6eefc50da30f58a"
  integrity sha1-165mfh3INIL4tw/Q9u78UNow9Yo=
  dependencies:
    call-bind-apply-helpers "^1.0.1"
    es-errors "^1.3.0"
    gopd "^1.2.0"

es-define-property@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/es-define-property/download/es-define-property-1.0.1.tgz#983eb2f9a6724e9303f61addf011c72e09e0b0fa"
  integrity sha1-mD6y+aZyTpMD9hrd8BHHLgngsPo=

es-errors@^1.3.0:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/es-errors/download/es-errors-1.3.0.tgz#05f75a25dab98e4fb1dcd5e1472c0546d5057c8f"
  integrity sha1-BfdaJdq5jk+x3NXhRywFRtUFfI8=

es-object-atoms@^1.0.0, es-object-atoms@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/es-object-atoms/download/es-object-atoms-1.1.1.tgz#1c4f2c4837327597ce69d2ca190a7fdd172338c1"
  integrity sha1-HE8sSDcydZfOadLKGQp/3RcjOME=
  dependencies:
    es-errors "^1.3.0"

es-set-tostringtag@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/es-set-tostringtag/download/es-set-tostringtag-2.1.0.tgz#f31dbbe0c183b00a6d26eb6325c810c0fd18bd4d"
  integrity sha1-8x274MGDsAptJutjJcgQwP0YvU0=
  dependencies:
    es-errors "^1.3.0"
    get-intrinsic "^1.2.6"
    has-tostringtag "^1.0.2"
    hasown "^2.0.2"

esbuild@0.8, esbuild@^0.8.52:
  version "0.8.57"
  resolved "http://r.npm.sankuai.com/esbuild/download/esbuild-0.8.57.tgz#a42d02bc2b57c70bcd0ef897fe244766bb6dd926"
  integrity sha1-pC0CvCtXxwvNDviX/iRHZrtt2SY=

escodegen@^2.0.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/escodegen/download/escodegen-2.1.0.tgz#ba93bbb7a43986d29d6041f99f5262da773e2e17"
  integrity sha1-upO7t6Q5htKdYEH5n1Ji2nc+Lhc=
  dependencies:
    esprima "^4.0.1"
    estraverse "^5.2.0"
    esutils "^2.0.2"
  optionalDependencies:
    source-map "~0.6.1"

esprima@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/esprima/download/esprima-4.0.1.tgz#13b04cdb3e6c5d19df91ab6987a8695619b0aa71"
  integrity sha1-E7BM2z5sXRnfkatph6hpVhmwqnE=

estraverse@^5.2.0:
  version "5.3.0"
  resolved "http://r.npm.sankuai.com/estraverse/download/estraverse-5.3.0.tgz#2eea5290702f26ab8fe5370370ff86c965d21123"
  integrity sha1-LupSkHAvJquP5TcDcP+GyWXSESM=

estree-walker@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/estree-walker/download/estree-walker-1.0.1.tgz#31bc5d612c96b704106b477e6dd5d8aa138cb700"
  integrity sha1-MbxdYSyWtwQQa0d+bdXYqhOMtwA=

esutils@^2.0.2:
  version "2.0.3"
  resolved "http://r.npm.sankuai.com/esutils/download/esutils-2.0.3.tgz#74d2eb4de0b8da1293711910d50775b9b710ef64"
  integrity sha1-dNLrTeC42hKTcRkQ1Qd1ubcQ72Q=

follow-redirects@^1.10.0:
  version "1.15.9"
  resolved "http://r.npm.sankuai.com/follow-redirects/download/follow-redirects-1.15.9.tgz#a604fa10e443bf98ca94228d9eebcc2e8a2c8ee1"
  integrity sha1-pgT6EORDv5jKlCKNnuvMLoosjuE=

form-data@^3.0.0:
  version "3.0.3"
  resolved "http://r.npm.sankuai.com/form-data/download/form-data-3.0.3.tgz#349c8f2c9d8f8f0c879ee0eb7cc0d300018d6b09"
  integrity sha1-NJyPLJ2PjwyHnuDrfMDTAAGNawk=
  dependencies:
    asynckit "^0.4.0"
    combined-stream "^1.0.8"
    es-set-tostringtag "^2.1.0"
    mime-types "^2.1.35"

fsevents@~2.3.1, fsevents@~2.3.2:
  version "2.3.3"
  resolved "http://r.npm.sankuai.com/fsevents/download/fsevents-2.3.3.tgz#cac6407785d03675a2a5e1a5305c697b347d90d6"
  integrity sha1-ysZAd4XQNnWipeGlMFxpezR9kNY=

function-bind@^1.1.2:
  version "1.1.2"
  resolved "http://r.npm.sankuai.com/function-bind/download/function-bind-1.1.2.tgz#2c02d864d97f3ea6c8830c464cbd11ab6eab7a1c"
  integrity sha1-LALYZNl/PqbIgwxGTL0Rq26rehw=

get-intrinsic@^1.2.6:
  version "1.3.0"
  resolved "http://r.npm.sankuai.com/get-intrinsic/download/get-intrinsic-1.3.0.tgz#743f0e3b6964a93a5491ed1bffaae054d7f98d01"
  integrity sha1-dD8OO2lkqTpUke0b/6rgVNf5jQE=
  dependencies:
    call-bind-apply-helpers "^1.0.2"
    es-define-property "^1.0.1"
    es-errors "^1.3.0"
    es-object-atoms "^1.1.1"
    function-bind "^1.1.2"
    get-proto "^1.0.1"
    gopd "^1.2.0"
    has-symbols "^1.1.0"
    hasown "^2.0.2"
    math-intrinsics "^1.1.0"

get-proto@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/get-proto/download/get-proto-1.0.1.tgz#150b3f2743869ef3e851ec0c49d15b1d14d00ee1"
  integrity sha1-FQs/J0OGnvPoUewMSdFbHRTQDuE=
  dependencies:
    dunder-proto "^1.0.1"
    es-object-atoms "^1.0.0"

gopd@^1.2.0:
  version "1.2.0"
  resolved "http://r.npm.sankuai.com/gopd/download/gopd-1.2.0.tgz#89f56b8217bdbc8802bd299df6d7f1081d7e51a1"
  integrity sha1-ifVrghe9vIgCvSmd9tfxCB1+UaE=

has-symbols@^1.0.3, has-symbols@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/has-symbols/download/has-symbols-1.1.0.tgz#fc9c6a783a084951d0b971fe1018de813707a338"
  integrity sha1-/JxqeDoISVHQuXH+EBjegTcHozg=

has-tostringtag@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/has-tostringtag/download/has-tostringtag-1.0.2.tgz#2cdc42d40bef2e5b4eeab7c01a73c54ce7ab5abc"
  integrity sha1-LNxC1AvvLltO6rfAGnPFTOerWrw=
  dependencies:
    has-symbols "^1.0.3"

hasown@^2.0.2:
  version "2.0.2"
  resolved "http://r.npm.sankuai.com/hasown/download/hasown-2.0.2.tgz#003eaf91be7adc372e84ec59dc37252cedb80003"
  integrity sha1-AD6vkb563DcuhOxZ3DclLO24AAM=
  dependencies:
    function-bind "^1.1.2"

html-encoding-sniffer@^2.0.1:
  version "2.0.1"
  resolved "http://r.npm.sankuai.com/html-encoding-sniffer/download/html-encoding-sniffer-2.0.1.tgz#42a6dc4fd33f00281176e8b23759ca4e4fa185f3"
  integrity sha1-QqbcT9M/ACgRduiyN1nKTk+hhfM=
  dependencies:
    whatwg-encoding "^1.0.5"

html2canvas@1.4.1:
  version "1.4.1"
  resolved "http://r.npm.sankuai.com/html2canvas/download/html2canvas-1.4.1.tgz#7cef1888311b5011d507794a066041b14669a543"
  integrity sha1-fO8YiDEbUBHVB3lKBmBBsUZppUM=
  dependencies:
    css-line-break "^2.1.0"
    text-segmentation "^1.0.3"

http-proxy-agent@^4.0.1:
  version "4.0.1"
  resolved "http://r.npm.sankuai.com/http-proxy-agent/download/http-proxy-agent-4.0.1.tgz#8a8c8ef7f5932ccf953c296ca8291b95aa74aa3a"
  integrity sha1-ioyO9/WTLM+VPClsqCkblap0qjo=
  dependencies:
    "@tootallnate/once" "1"
    agent-base "6"
    debug "4"

https-proxy-agent@^5.0.0:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/https-proxy-agent/download/https-proxy-agent-5.0.1.tgz#c59ef224a04fe8b754f3db0063a25ea30d0005d6"
  integrity sha1-xZ7yJKBP6LdU89sAY6Jeow0ABdY=
  dependencies:
    agent-base "6"
    debug "4"

iconv-lite@0.4.24:
  version "0.4.24"
  resolved "http://r.npm.sankuai.com/iconv-lite/download/iconv-lite-0.4.24.tgz#2022b4b25fbddc21d2f524974a474aafe733908b"
  integrity sha1-ICK0sl+93CHS9SSXSkdKr+czkIs=
  dependencies:
    safer-buffer ">= 2.1.2 < 3"

is-core-module@^2.16.0:
  version "2.16.1"
  resolved "http://r.npm.sankuai.com/is-core-module/download/is-core-module-2.16.1.tgz#2a98801a849f43e2add644fbb6bc6229b19a4ef4"
  integrity sha1-KpiAGoSfQ+Kt1kT7trxiKbGaTvQ=
  dependencies:
    hasown "^2.0.2"

is-potential-custom-element-name@^1.0.1:
  version "1.0.1"
  resolved "http://r.npm.sankuai.com/is-potential-custom-element-name/download/is-potential-custom-element-name-1.0.1.tgz#171ed6f19e3ac554394edf78caa05784a45bebb5"
  integrity sha1-Fx7W8Z46xVQ5Tt94yqBXhKRb67U=

jsdom@16.7.0:
  version "16.7.0"
  resolved "http://r.npm.sankuai.com/jsdom/download/jsdom-16.7.0.tgz#918ae71965424b197c819f8183a754e18977b710"
  integrity sha1-kYrnGWVCSxl8gZ+Bg6dU4Yl3txA=
  dependencies:
    abab "^2.0.5"
    acorn "^8.2.4"
    acorn-globals "^6.0.0"
    cssom "^0.4.4"
    cssstyle "^2.3.0"
    data-urls "^2.0.0"
    decimal.js "^10.2.1"
    domexception "^2.0.1"
    escodegen "^2.0.0"
    form-data "^3.0.0"
    html-encoding-sniffer "^2.0.1"
    http-proxy-agent "^4.0.1"
    https-proxy-agent "^5.0.0"
    is-potential-custom-element-name "^1.0.1"
    nwsapi "^2.2.0"
    parse5 "6.0.1"
    saxes "^5.0.1"
    symbol-tree "^3.2.4"
    tough-cookie "^4.0.0"
    w3c-hr-time "^1.0.2"
    w3c-xmlserializer "^2.0.0"
    webidl-conversions "^6.1.0"
    whatwg-encoding "^1.0.5"
    whatwg-mimetype "^2.3.0"
    whatwg-url "^8.5.0"
    ws "^7.4.6"
    xml-name-validator "^3.0.0"

lodash@^4.7.0:
  version "4.17.21"
  resolved "http://r.npm.sankuai.com/lodash/download/lodash-4.17.21.tgz#679591c564c3bffaae8454cf0b3df370c3d6911c"
  integrity sha1-Z5WRxWTDv/quhFTPCz3zcMPWkRw=

math-intrinsics@^1.1.0:
  version "1.1.0"
  resolved "http://r.npm.sankuai.com/math-intrinsics/download/math-intrinsics-1.1.0.tgz#a0dd74be81e2aa5c2f27e65ce283605ee4e2b7f9"
  integrity sha1-oN10voHiqlwvJ+Zc4oNgXuTit/k=

mime-db@1.52.0:
  version "1.52.0"
  resolved "http://r.npm.sankuai.com/mime-db/download/mime-db-1.52.0.tgz#bbabcdc02859f4987301c856e3387ce5ec43bf70"
  integrity sha1-u6vNwChZ9JhzAchW4zh85exDv3A=

mime-types@^2.1.35:
  version "2.1.35"
  resolved "http://r.npm.sankuai.com/mime-types/download/mime-types-2.1.35.tgz#381a871b62a734450660ae3deee44813f70d959a"
  integrity sha1-OBqHG2KnNEUGYK497uRIE/cNlZo=
  dependencies:
    mime-db "1.52.0"

ms@^2.1.3:
  version "2.1.3"
  resolved "http://r.npm.sankuai.com/ms/download/ms-2.1.3.tgz#574c8138ce1d2b5861f0b44579dbadd60c6615b2"
  integrity sha1-V0yBOM4dK1hh8LRFedut1gxmFbI=

nanoid@^3.3.8:
  version "3.3.11"
  resolved "http://r.npm.sankuai.com/nanoid/download/nanoid-3.3.11.tgz#4f4f112cefbe303202f2199838128936266d185b"
  integrity sha1-T08RLO++MDIC8hmYOBKJNiZtGFs=

nwsapi@^2.2.0:
  version "2.2.20"
  resolved "http://r.npm.sankuai.com/nwsapi/download/nwsapi-2.2.20.tgz#22e53253c61e7b0e7e93cef42c891154bcca11ef"
  integrity sha1-IuUyU8Yeew5+k870LIkRVLzKEe8=

parse5@6.0.1:
  version "6.0.1"
  resolved "http://r.npm.sankuai.com/parse5/download/parse5-6.0.1.tgz#e1a1c085c569b3dc08321184f19a39cc27f7c30b"
  integrity sha1-4aHAhcVps9wIMhGE8Zo5zCf3wws=

path-parse@^1.0.7:
  version "1.0.7"
  resolved "http://r.npm.sankuai.com/path-parse/download/path-parse-1.0.7.tgz#fbc114b60ca42b30d9daf5858e4bd68bbedb6735"
  integrity sha1-+8EUtgykKzDZ2vWFjkvWi77bZzU=

picocolors@^1.1.1:
  version "1.1.1"
  resolved "http://r.npm.sankuai.com/picocolors/download/picocolors-1.1.1.tgz#3d321af3eab939b083c8f929a1d12cda81c26b6b"
  integrity sha1-PTIa8+q5ObCDyPkpodEs2oHCa2s=

picomatch@^2.2.2:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/picomatch/download/picomatch-2.3.1.tgz#3ba3833733646d9d3e4995946c1365a67fb07a42"
  integrity sha1-O6ODNzNkbZ0+SZWUbBNlpn+wekI=

postcss@^8.2.1:
  version "8.5.3"
  resolved "http://r.npm.sankuai.com/postcss/download/postcss-8.5.3.tgz#1463b6f1c7fb16fe258736cba29a2de35237eafb"
  integrity sha1-FGO28cf7Fv4lhzbLopot41I36vs=
  dependencies:
    nanoid "^3.3.8"
    picocolors "^1.1.1"
    source-map-js "^1.2.1"

psl@^1.1.33:
  version "1.15.0"
  resolved "http://r.npm.sankuai.com/psl/download/psl-1.15.0.tgz#bdace31896f1d97cec6a79e8224898ce93d974c6"
  integrity sha1-vazjGJbx2XzsannoIkiYzpPZdMY=
  dependencies:
    punycode "^2.3.1"

punycode@^2.1.1, punycode@^2.3.1:
  version "2.3.1"
  resolved "http://r.npm.sankuai.com/punycode/download/punycode-2.3.1.tgz#027422e2faec0b25e1549c3e1bd8309b9133b6e5"
  integrity sha1-AnQi4vrsCyXhVJw+G9gwm5EztuU=

querystringify@^2.1.1:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/querystringify/download/querystringify-2.2.0.tgz#3345941b4153cb9d082d8eee4cda2016a9aef7f6"
  integrity sha1-M0WUG0FTy50ILY7uTNogFqmu9/Y=

requires-port@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/requires-port/download/requires-port-1.0.0.tgz#925d2601d39ac485e091cf0da5c6e694dc3dcaff"
  integrity sha1-kl0mAdOaxIXgkc8NpcbmlNw9yv8=

resolve@^1.19.0:
  version "1.22.10"
  resolved "http://r.npm.sankuai.com/resolve/download/resolve-1.22.10.tgz#b663e83ffb09bbf2386944736baae803029b8b39"
  integrity sha1-tmPoP/sJu/I4aURza6roAwKbizk=
  dependencies:
    is-core-module "^2.16.0"
    path-parse "^1.0.7"
    supports-preserve-symlinks-flag "^1.0.0"

rollup@^2.38.5:
  version "2.79.2"
  resolved "http://r.npm.sankuai.com/rollup/download/rollup-2.79.2.tgz#f150e4a5db4b121a21a747d762f701e5e9f49090"
  integrity sha1-8VDkpdtLEhohp0fXYvcB5en0kJA=
  optionalDependencies:
    fsevents "~2.3.2"

"safer-buffer@>= 2.1.2 < 3":
  version "2.1.2"
  resolved "http://r.npm.sankuai.com/safer-buffer/download/safer-buffer-2.1.2.tgz#44fa161b0187b9549dd84bb91802f9bd8385cd6a"
  integrity sha1-RPoWGwGHuVSd2Eu5GAL5vYOFzWo=

saxes@^5.0.1:
  version "5.0.1"
  resolved "http://r.npm.sankuai.com/saxes/download/saxes-5.0.1.tgz#eebab953fa3b7608dbe94e5dadb15c888fa6696d"
  integrity sha1-7rq5U/o7dgjb6U5drbFciI+maW0=
  dependencies:
    xmlchars "^2.2.0"

source-map-js@^1.2.1:
  version "1.2.1"
  resolved "http://r.npm.sankuai.com/source-map-js/download/source-map-js-1.2.1.tgz#1ce5650fddd87abc099eda37dcff024c2667ae46"
  integrity sha1-HOVlD93YerwJnto33P8CTCZnrkY=

source-map@~0.6.1:
  version "0.6.1"
  resolved "http://r.npm.sankuai.com/source-map/download/source-map-0.6.1.tgz#74722af32e9614e9c287a8d0bbde48b5e2f1a263"
  integrity sha1-dHIq8y6WFOnCh6jQu95IteLxomM=

supports-preserve-symlinks-flag@^1.0.0:
  version "1.0.0"
  resolved "http://r.npm.sankuai.com/supports-preserve-symlinks-flag/download/supports-preserve-symlinks-flag-1.0.0.tgz#6eda4bd344a3c94aea376d4cc31bc77311039e09"
  integrity sha1-btpL00SjyUrqN21MwxvHcxEDngk=

symbol-tree@^3.2.4:
  version "3.2.4"
  resolved "http://r.npm.sankuai.com/symbol-tree/download/symbol-tree-3.2.4.tgz#430637d248ba77e078883951fb9aa0eed7c63fa2"
  integrity sha1-QwY30ki6d+B4iDlR+5qg7tfGP6I=

text-segmentation@^1.0.2, text-segmentation@^1.0.3:
  version "1.0.3"
  resolved "http://r.npm.sankuai.com/text-segmentation/download/text-segmentation-1.0.3.tgz#52a388159efffe746b24a63ba311b6ac9f2d7943"
  integrity sha1-UqOIFZ7//nRrJKY7oxG2rJ8teUM=
  dependencies:
    utrie "^1.0.2"

tough-cookie@^4.0.0:
  version "4.1.4"
  resolved "http://r.npm.sankuai.com/tough-cookie/download/tough-cookie-4.1.4.tgz#945f1461b45b5a8c76821c33ea49c3ac192c1b36"
  integrity sha1-lF8UYbRbWox2ghwz6knDrBksGzY=
  dependencies:
    psl "^1.1.33"
    punycode "^2.1.1"
    universalify "^0.2.0"
    url-parse "^1.5.3"

tr46@^2.1.0:
  version "2.1.0"
  resolved "http://r.npm.sankuai.com/tr46/download/tr46-2.1.0.tgz#fa87aa81ca5d5941da8cbf1f9b749dc969a4e240"
  integrity sha1-+oeqgcpdWUHajL8fm3SdyWmk4kA=
  dependencies:
    punycode "^2.1.1"

typescript@^4.2.3:
  version "4.9.5"
  resolved "http://r.npm.sankuai.com/typescript/download/typescript-4.9.5.tgz#095979f9bcc0d09da324d58d03ce8f8374cbe65a"
  integrity sha1-CVl5+bzA0J2jJNWNA86Pg3TL5lo=

universalify@^0.2.0:
  version "0.2.0"
  resolved "http://r.npm.sankuai.com/universalify/download/universalify-0.2.0.tgz#6451760566fa857534745ab1dde952d1b1761be0"
  integrity sha1-ZFF2BWb6hXU0dFqx3elS0bF2G+A=

url-parse@^1.5.3:
  version "1.5.10"
  resolved "http://r.npm.sankuai.com/url-parse/download/url-parse-1.5.10.tgz#9d3c2f736c1d75dd3bd2be507dcc111f1e2ea9c1"
  integrity sha1-nTwvc2wddd070r5QfcwRHx4uqcE=
  dependencies:
    querystringify "^2.1.1"
    requires-port "^1.0.0"

utrie@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/utrie/download/utrie-1.0.2.tgz#d42fe44de9bc0119c25de7f564a6ed1b2c87a645"
  integrity sha1-1C/kTem8ARnCXef1ZKbtGyyHpkU=
  dependencies:
    base64-arraybuffer "^1.0.2"

vite@~2.0.5:
  version "2.0.5"
  resolved "http://r.npm.sankuai.com/vite/download/vite-2.0.5.tgz#ac46857a3fa8686d077921e61bd48a986931df1d"
  integrity sha1-rEaFej+oaG0HeSHmG9SKmGkx3x0=
  dependencies:
    esbuild "^0.8.52"
    postcss "^8.2.1"
    resolve "^1.19.0"
    rollup "^2.38.5"
  optionalDependencies:
    fsevents "~2.3.1"

w3c-hr-time@^1.0.2:
  version "1.0.2"
  resolved "http://r.npm.sankuai.com/w3c-hr-time/download/w3c-hr-time-1.0.2.tgz#0a89cdf5cc15822df9c360543676963e0cc308cd"
  integrity sha1-ConN9cwVgi35w2BUNnaWPgzDCM0=
  dependencies:
    browser-process-hrtime "^1.0.0"

w3c-xmlserializer@^2.0.0:
  version "2.0.0"
  resolved "http://r.npm.sankuai.com/w3c-xmlserializer/download/w3c-xmlserializer-2.0.0.tgz#3e7104a05b75146cc60f564380b7f683acf1020a"
  integrity sha1-PnEEoFt1FGzGD1ZDgLf2g6zxAgo=
  dependencies:
    xml-name-validator "^3.0.0"

webidl-conversions@^5.0.0:
  version "5.0.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-5.0.0.tgz#ae59c8a00b121543a2acc65c0434f57b0fc11aff"
  integrity sha1-rlnIoAsSFUOirMZcBDT1ew/BGv8=

webidl-conversions@^6.1.0:
  version "6.1.0"
  resolved "http://r.npm.sankuai.com/webidl-conversions/download/webidl-conversions-6.1.0.tgz#9111b4d7ea80acd40f5270d666621afa78b69514"
  integrity sha1-kRG01+qArNQPUnDWZmIa+ni2lRQ=

whatwg-encoding@^1.0.5:
  version "1.0.5"
  resolved "http://r.npm.sankuai.com/whatwg-encoding/download/whatwg-encoding-1.0.5.tgz#5abacf777c32166a51d085d6b4f3e7d27113ddb0"
  integrity sha1-WrrPd3wyFmpR0IXWtPPn0nET3bA=
  dependencies:
    iconv-lite "0.4.24"

whatwg-mimetype@^2.3.0:
  version "2.3.0"
  resolved "http://r.npm.sankuai.com/whatwg-mimetype/download/whatwg-mimetype-2.3.0.tgz#3d4b1e0312d2079879f826aff18dbeeca5960fbf"
  integrity sha1-PUseAxLSB5h5+Cav8Y2+7KWWD78=

whatwg-url@^8.0.0, whatwg-url@^8.5.0:
  version "8.7.0"
  resolved "http://r.npm.sankuai.com/whatwg-url/download/whatwg-url-8.7.0.tgz#656a78e510ff8f3937bc0bcbe9f5c0ac35941b77"
  integrity sha1-ZWp45RD/jzk3vAvL6fXArDWUG3c=
  dependencies:
    lodash "^4.7.0"
    tr46 "^2.1.0"
    webidl-conversions "^6.1.0"

ws@^7.4.6:
  version "7.5.10"
  resolved "http://r.npm.sankuai.com/ws/download/ws-7.5.10.tgz#58b5c20dc281633f6c19113f39b349bd8bd558d9"
  integrity sha1-WLXCDcKBYz9sGRE/ObNJvYvVWNk=

xml-name-validator@^3.0.0:
  version "3.0.0"
  resolved "http://r.npm.sankuai.com/xml-name-validator/download/xml-name-validator-3.0.0.tgz#6ae73e06de4d8c6e47f9fb181f78d648ad457c6a"
  integrity sha1-auc+Bt5NjG5H+fsYH3jWSK1FfGo=

xmlchars@^2.2.0:
  version "2.2.0"
  resolved "http://r.npm.sankuai.com/xmlchars/download/xmlchars-2.2.0.tgz#060fe1bcb7f9c76fe2a17db86a9bc3ab894210cb"
  integrity sha1-Bg/hvLf5x2/ioX24apvDq4lCEMs=
