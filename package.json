{"name": "@gfe/talos-ai-coding-analysis", "version": "0.1.0", "description": "美点小程序通用sdk", "files": ["build"], "main": "./build/talos-ai-coding-analysis.umd.js", "module": "./build/talos-ai-coding-analysis.js", "exports": {".": {"import": "./build/talos-ai-coding-analysis.es.js", "require": "./build/talos-ai-coding-analysis.umd.js", "commonjs": "./build/talos-ai-coding-analysis.cjs.js"}}, "repository": {"url": "", "type": "git"}, "author": "yaoyan03", "scripts": {"dev": "vite", "build": "tsc && vite build", "serve": "vite preview", "doc:dev": "ax doc start", "doc:build": "ax doc build", "doc:publish": "ax doc publish", "lint": "echo lint", "test": "echo test"}, "devDependencies": {"typescript": "^4.2.3", "vite": "^2.2.3"}, "dependencies": {"@nibfe/white-screen-sdk": "0.2.4", "esbuild": "0.8", "html2canvas": "1.4.1", "vite": "~2.0.5"}}