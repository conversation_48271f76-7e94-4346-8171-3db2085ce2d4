const fs = require('fs');
const path = require('path');

// 递归查找目录中的所有js文件
function findJsFiles(dir, fileList = []) {
  const files = fs.readdirSync(dir);
  files.forEach(file => {
    const filePath = path.join(dir, file);
    if (fs.statSync(filePath).isDirectory()) {
      findJsFiles(filePath, fileList);
    } else if (file.endsWith('.js')) {
      fileList.push(filePath);
    }
  });
  return fileList;
}

// 修复代码中的空catch块
function fixEmptyCatchBlocks(filePath) {
  let content = fs.readFileSync(filePath, 'utf8');
  let modified = false;
  
  // 替换空catch块
  const newContent = content.replace(/} catch {/g, '} catch (error) {');
  
  if (newContent !== content) {
    fs.writeFileSync(filePath, newContent);
    console.log(`Fixed empty catch blocks in: ${filePath}`);
    modified = true;
  }
  
  return modified;
}

try {
  console.log('Fixing Vite compatibility issues...');
  const viteNodeDir = path.resolve('./node_modules/vite/dist/node');
  
  if (fs.existsSync(viteNodeDir)) {
    const jsFiles = findJsFiles(viteNodeDir);
    let fixedCount = 0;
    
    jsFiles.forEach(file => {
      if (fixEmptyCatchBlocks(file)) {
        fixedCount++;
      }
    });
    
    console.log(`Fixed ${fixedCount} files in Vite.`);
  } else {
    console.log('Vite node directory not found.');
  }

  // 同时检查esbuild的安装脚本
  const esbuildInstallPath = path.resolve('./node_modules/vite/node_modules/esbuild/install.js');
  if (fs.existsSync(esbuildInstallPath)) {
    if (fixEmptyCatchBlocks(esbuildInstallPath)) {
      console.log('Fixed esbuild install.js');
    }
  }
} catch (err) {
  console.error('Error fixing Vite code:', err);
  process.exit(1);
} 