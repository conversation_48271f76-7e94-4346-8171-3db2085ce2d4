# white-screen-monitor

> 同时视觉来监控是否白屏

## Build Setup

``` bash
# install dependencies
$ yarn install

# serve with hot reload at localhost:8080
$ yarn run start

# build for production and launch server
$ yarn run build
```

## Unit Test

``` bash
# run unit test for your code
$ yarn run test
```

## Example Running 

> ax doc Useage: https://tetris.sankuai.com/app/static/tetris-doc/docs/1x/user-document.html

```bash
# run example in Markdown
$ ax doc start
```

<!--code-start-->
<!--demo-->

```html
<template>
    <div>
      <button @click="click">点击</button>
    </div>
</template>

<script>
import Hello from 'white-screen-monitor'

export default {
  methods: {
    click() {
      new Hello()
    }
  }
}
</script>
```

<!--code-end-->